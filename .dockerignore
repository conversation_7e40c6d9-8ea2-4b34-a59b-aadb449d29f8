# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
build/

# Configuration files (keep example)
config.yaml
config.yml
config.json

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Docker volumes
postgres_data/
redis_data/

# Test coverage
coverage.out
coverage.html

# Temporary files
tmp/
temp/

# Git directory
.git
.gitignore

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation that's not needed for runtime
README.md
docs/README.md
docs/*.md

# Keep the generated swagger docs for the build
# !docs/docs.go
# !docs/swagger.json
# !docs/swagger.yaml
