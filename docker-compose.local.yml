version: '3.8'

services:
  # Local app service to test env-var-only configuration
  stellar-api:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      # App
      ENVIRONMENT: development
      PORT: 8080

      # Database (point to the postgres service)
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: stellar
      DB_USER: stellar_user
      DB_PASSWORD: stellar_password
      DB_SSLMODE: disable

      # JWT / App URLs (dummy but required)
      SECRET_KEY: test-secret-key
      BACKEND_URL: http://localhost:8080
      FRONTEND_URL: http://localhost:3000

      # Okta (dummy but required)
      OKTA_DOMAIN: dev.okta.com
      OKTA_CLIENT_ID: dummy-client-id
      OKTA_CLIENT_SECRET: dummy-client-secret
      OKTA_REDIRECT_URI: http://localhost:8080/login/callback
      IDP_ADMIN_GROUP: StellarAdmins

      # SCIM (dummy but required)
      SCIM_BEARER_TOKEN: dummy-scim-token

    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "8080:8080"

networks:
  default:
    name: stellar-network

