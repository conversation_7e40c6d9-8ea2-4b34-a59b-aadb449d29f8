package fixtures

import (
	"fmt"
	"log"

	"stellar-go/internal/config"
	"stellar-go/internal/models"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// SetupTestDB creates a test database using testcontainers
func SetupTestDB() (*gorm.DB, *config.Config, error) {
	// For now, always use the simple setup to avoid testcontainers complexity
	return setupSQLiteDB()
}

// setupSQLiteDB creates an in-memory SQLite database for faster tests
func setupSQLiteDB() (*gorm.DB, *config.Config, error) {
	// Use SQLite in-memory database for testing
	cfg := &config.Config{
		Environment: "test",
		JWT: config.JWTConfig{
			SecretKey:   "test-secret-key",
			BackendURL:  "http://localhost:8080",
			FrontendURL: "http://localhost:3000",
		},
		SCIM: config.SCIMConfig{
			BearerToken: "test-scim-token",
		},
	}

	// Use SQLite in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to open SQLite database: %w", err)
	}

	// Auto-migrate for tests
	err = db.AutoMigrate(&models.User{}, &models.Orbstar{})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to migrate test database: %w", err)
	}

	return db, cfg, nil
}

// TeardownTestDB cleans up the test database
func TeardownTestDB(db *gorm.DB) {
	if db != nil {
		sqlDB, err := db.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
}

// CleanDatabase removes all data from test database
func CleanDatabase(db *gorm.DB) {
	// Delete in order to respect foreign key constraints
	// Use the actual table names defined in the models
	db.Exec("DELETE FROM orbstar")
	db.Exec("DELETE FROM userprofile")

	// For SQLite, we don't need to reset sequences as they auto-increment
	// For PostgreSQL in real tests, you would reset sequences here
}

// CreateTestUser creates a test user in the database
func CreateTestUser(db *gorm.DB, email, firstName, lastName string, admin bool) *models.User {
	user := &models.User{
		Email:     email,
		FirstName: firstName,
		LastName:  lastName,
		Active:    true,
		Admin:     admin,
	}

	if err := db.Create(user).Error; err != nil {
		log.Fatalf("Failed to create test user: %v", err)
	}

	return user
}

// CreateTestOrbstar creates a test orbstar in the database
func CreateTestOrbstar(db *gorm.DB, giverID, receiverID uint, value models.CompanyValue, description string) *models.Orbstar {
	orbstar := &models.Orbstar{
		GiverID:     giverID,
		ReceiverID:  receiverID,
		Value:       value,
		Description: description,
	}

	if err := db.Create(orbstar).Error; err != nil {
		log.Fatalf("Failed to create test orbstar: %v", err)
	}

	// Preload relationships
	db.Preload("Giver").Preload("Receiver").First(orbstar, orbstar.ID)

	return orbstar
}
