package fixtures

import (
	"log"

	"stellar-go/internal/auth"
	"stellar-go/internal/config"
)

// GenerateTestJWT generates a JWT token for testing
func GenerateTestJWT(cfg *config.Config, userID uint) string {
	jwtManager := auth.NewJWTManager(cfg)

	accessToken, _, err := jwtManager.GenerateTokenPair(userID)
	if err != nil {
		log.Fatalf("Failed to generate test JWT: %v", err)
	}

	return accessToken
}
