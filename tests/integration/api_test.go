package integration

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"stellar-go/internal/api"
	"stellar-go/internal/config"
	"stellar-go/internal/models"
	"stellar-go/tests/fixtures"

	_ "stellar-go/docs" // Import generated docs

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"
)

type APITestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine
	cfg    *config.Config
}

func (suite *APITestSuite) SetupSuite() {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Initialize test database
	var err error
	suite.db, suite.cfg, err = fixtures.SetupTestDB()
	suite.Require().NoError(err)

	// Setup router
	suite.router = gin.New()
	api.SetupRoutes(suite.router, suite.db, suite.cfg)
}

func (suite *APITestSuite) TearDownSuite() {
	fixtures.TeardownTestDB(suite.db)
}

func (suite *APITestSuite) SetupTest() {
	// Clean database before each test
	fixtures.CleanDatabase(suite.db)
}

func (suite *APITestSuite) TestHomepage() {
	req, _ := http.NewRequest("GET", "/", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
	assert.Contains(suite.T(), w.Header().Get("Content-Type"), "text/html")
	assert.Contains(suite.T(), w.Body.String(), "Stellar | Be lekker")
	assert.Contains(suite.T(), w.Body.String(), "<!DOCTYPE html>")
}

func (suite *APITestSuite) TestHealthCheck() {
	req, _ := http.NewRequest("GET", "/healthz", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response models.HealthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.HealthStatusHealthy, response.Status)
	assert.Equal(suite.T(), "Stellar | Be lekker", response.Message)
	assert.Contains(suite.T(), response.Services, "database")
	assert.Equal(suite.T(), models.HealthStatusHealthy, response.Services["database"].Status)
	assert.Empty(suite.T(), response.Services["database"].Error)
}

func (suite *APITestSuite) TestHealthCheckWithDatabaseFailure() {
	// Close the database connection to simulate failure
	sqlDB, err := suite.db.DB()
	assert.NoError(suite.T(), err)
	sqlDB.Close()

	// Create a new router with the closed database
	router := gin.New()
	api.SetupRoutes(router, suite.db, suite.cfg)

	req, _ := http.NewRequest("GET", "/healthz", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusServiceUnavailable, w.Code)

	var response models.HealthResponse
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.HealthStatusUnhealthy, response.Status)
	assert.Equal(suite.T(), "Stellar | Be lekker", response.Message)
	assert.Contains(suite.T(), response.Services, "database")
	assert.Equal(suite.T(), models.HealthStatusUnhealthy, response.Services["database"].Status)
	assert.NotEmpty(suite.T(), response.Services["database"].Error)

	// Reinitialize the database for subsequent tests
	suite.db, suite.cfg, err = fixtures.SetupTestDB()
	suite.Require().NoError(err)
	suite.router = gin.New()
	api.SetupRoutes(suite.router, suite.db, suite.cfg)
}

func (suite *APITestSuite) TestFailEndpoint() {
	req, _ := http.NewRequest("GET", "/fail", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusInternalServerError, w.Code)

	var response models.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "test_error", response.Code)
}

func (suite *APITestSuite) TestGetValuesEndpoint() {
	// Create a test user and get JWT token
	user := fixtures.CreateTestUser(suite.db, "<EMAIL>", "Test", "User", false)
	token := fixtures.GenerateTestJWT(suite.cfg, user.ID)

	req, _ := http.NewRequest("GET", "/values", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response models.ValuesResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), response.Values, 6)
	assert.Contains(suite.T(), response.Values, "Self Development")
	assert.Contains(suite.T(), response.Values, "Innovation")
}

func (suite *APITestSuite) TestCreateOrbstar() {
	// Create test users
	giver := fixtures.CreateTestUser(suite.db, "<EMAIL>", "Giver", "User", false)
	receiver := fixtures.CreateTestUser(suite.db, "<EMAIL>", "Receiver", "User", false)
	token := fixtures.GenerateTestJWT(suite.cfg, giver.ID)

	// Create orbstar request
	request := models.CreateOrbstarRequest{
		Receiver:    float64(receiver.ID),
		Description: "Great work on the project!",
		Value:       models.Innovation,
	}

	body, _ := json.Marshal(request)
	req, _ := http.NewRequest("POST", "/orbstars", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response []models.OrbstarResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), response, 1)
	assert.Equal(suite.T(), "Innovation", response[0].Value)
	assert.Equal(suite.T(), "Great work on the project!", response[0].Description)
}

func (suite *APITestSuite) TestCreateOrbstarSelfRecognition() {
	// Create test user
	user := fixtures.CreateTestUser(suite.db, "<EMAIL>", "Test", "User", false)
	token := fixtures.GenerateTestJWT(suite.cfg, user.ID)

	// Try to create orbstar for self
	request := models.CreateOrbstarRequest{
		Receiver:    float64(user.ID),
		Description: "Self recognition attempt",
		Value:       models.Innovation,
	}

	body, _ := json.Marshal(request)
	req, _ := http.NewRequest("POST", "/orbstars", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	var response models.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "self_recognition", response.Code)
}

func (suite *APITestSuite) TestGetUsersWithAuth() {
	// Create test user
	user := fixtures.CreateTestUser(suite.db, "<EMAIL>", "Test", "User", false)
	token := fixtures.GenerateTestJWT(suite.cfg, user.ID)

	req, _ := http.NewRequest("GET", "/users", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response []models.User
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), response, 1) // Only visible users
}

func (suite *APITestSuite) TestGetUsersWithoutAuth() {
	req, _ := http.NewRequest("GET", "/users", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)

	var response models.ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "missing_auth_header", response.Code)
}

func (suite *APITestSuite) TestSCIMCreateUser() {
	scimUser := map[string]interface{}{
		"schemas":  []string{"urn:ietf:params:scim:schemas:core:2.0:User"},
		"userName": "<EMAIL>",
		"name": map[string]string{
			"givenName":  "New",
			"familyName": "User",
		},
		"emails": []map[string]interface{}{
			{
				"value":   "<EMAIL>",
				"primary": true,
			},
		},
		"active": true,
	}

	body, _ := json.Marshal(scimUser)
	req, _ := http.NewRequest("POST", "/scim/v2/Users", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.cfg.SCIM.BearerToken)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "<EMAIL>", response["userName"])
}

func TestAPITestSuite(t *testing.T) {
	suite.Run(t, new(APITestSuite))
}
