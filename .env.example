# Stellar Go API Environment Variables
# Copy this file to .env and update with your values

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=stellar
DB_USER=stellar_user
DB_PASSWORD=stellar_password
DB_SSLMODE=disable

# Application Configuration
PORT=8080
ENVIRONMENT=development

# JWT Configuration (CHANGE THESE IN PRODUCTION!)
SECRET_KEY=dev-secret-key-change-in-production-12345
BACKEND_URL=http://localhost:8080
FRONTEND_URL=http://localhost:3000

# Okta Configuration (replace with your actual values)
OKTA_DOMAIN=dev.okta.com
OKTA_CLIENT_ID=dev-client-id
OKTA_CLIENT_SECRET=dev-client-secret
OKTA_REDIRECT_URI=http://localhost:8080/login/callback
IDP_ADMIN_GROUP=StellarAdmins

# SCIM Configuration
SCIM_BEARER_TOKEN=dev-scim-bearer-token-12345

# Slack Configuration (Optional - leave empty to disable)
SLACK_TOKEN=
SLACK_NOTIFICATION_CHANNEL_ID=

SLACK_SIGNING_SECRET=

# CORS Configuration
# Comma-separated list of allowed origins for CORS
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Content-Length,Accept-Encoding,X-CSRF-Token,Authorization,accept,origin,Cache-Control,X-Requested-With

# Proxy / Ingress (Traefik/NGINX) configuration
# TRUSTED_PROXIES: comma-separated list of proxy IPs/CIDRs to trust (e.g., 10.0.0.0/8,**********/12)
# Leave empty to trust none (recommended for local dev without proxy)
TRUSTED_PROXIES=
# TRUSTED_PLATFORM: set to X-Forwarded-For when behind Traefik or a similar ingress
# Common values: X-Forwarded-For, X-Real-IP
TRUSTED_PLATFORM=



# Sentry Configuration (Optional - set SENTRY_DSN to enable)
# Your Sentry DSN from your Sentry project settings
SENTRY_DSN=
# Override the environment Sentry reports (defaults to ENVIRONMENT if empty)
SENTRY_ENVIRONMENT=
# Release identifier for tracking deployments (e.g. stellar-go@1.0.0 or a git SHA)
SENTRY_RELEASE=
# Error event sampling rate (0.0 to 1.0)
SENTRY_SAMPLE_RATE=1.0
# Performance tracing sampling rate (0.0 to 1.0)
SENTRY_TRACES_SAMPLE_RATE=0.1
# Enable Sentry SDK debug logging (true/false)
SENTRY_DEBUG=false
