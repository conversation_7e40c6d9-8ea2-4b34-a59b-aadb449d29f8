* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    height: 100vh;
    font-family: '<PERSON><PERSON>', 'Raleway', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: radial-gradient(ellipse at center, #1e3c72 0%, #2a5298 50%, #0f1419 100%);
    overflow: hidden;
    position: relative;
}

/* Hyperspace starfield */
#hyperspace-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 0;
}

.container {
    position: relative;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

/* Logo */
.logo {
    position: relative;
    margin-bottom: 2rem;
}

.logo img {
    width: 250px;
    height: auto;
    filter: drop-shadow(0 0 30px rgba(78, 205, 196, 0.5));
}

/* Text in the beam */
.stellar-text {
    color: white;
    text-align: center;
    margin-top: 1rem;
}

.stellar-title {
    font-size: 3.5rem;
    font-weight: 700;
    letter-spacing: 0.3em;
    margin-bottom: 0.5rem;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.stellar-subtitle {
    font-size: 1.2rem;
    font-weight: 400;
    opacity: 0.9;
    letter-spacing: 0.1em;
}

@media (max-width: 768px) {
    .stellar-title {
        font-size: 2.5rem;
    }
    .stellar-subtitle {
        font-size: 1rem;
    }
    .logo img {
        width: 180px;
    }
}
