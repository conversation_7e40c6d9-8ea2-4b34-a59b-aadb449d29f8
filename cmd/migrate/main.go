package main

import (
	"errors"
	"flag"
	"log"
	"os"

	"stellar-go/internal/config"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	postgresDriver "gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	var (
		direction = flag.String("direction", "up", "Migration direction: up or down")
		steps     = flag.Int("steps", 0, "Number of migration steps (0 for all)")
		version   = flag.Uint("version", 0, "Migrate to specific version")
	)
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Connect to database
	db, err := gorm.Open(postgresDriver.Open(cfg.GetDatabaseDSN()), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB: %v", err)
	}
	defer sqlDB.Close()

	// Create postgres driver
	driver, err := postgres.WithInstance(sqlDB, &postgres.Config{})
	if err != nil {
		log.Fatalf("Failed to create postgres driver: %v", err)
	}

	// Create migrate instance
	m, err := migrate.NewWithDatabaseInstance(
		"file://migrations",
		"postgres",
		driver,
	)
	if err != nil {
		log.Fatalf("Failed to create migrate instance: %v", err)
	}

	// Execute migration based on flags
	switch {
	case *version > 0:
		log.Printf("Migrating to version %d...", *version)
		err = m.Migrate(*version)
	case *direction == "down":
		if *steps > 0 {
			log.Printf("Migrating down %d steps...", *steps)
			err = m.Steps(-*steps)
		} else {
			log.Println("Migrating down all...")
			err = m.Down()
		}
	default: // up
		if *steps > 0 {
			log.Printf("Migrating up %d steps...", *steps)
			err = m.Steps(*steps)
		} else {
			log.Println("Migrating up all...")
			err = m.Up()
		}
	}

	if err != nil {
		if errors.Is(err, migrate.ErrNoChange) {
			log.Println("No migrations to apply")
			os.Exit(0)
		}
		log.Fatalf("Migration failed: %v", err)
	}

	// Get current version
	versionCurrent, dirty, err := m.Version()
	if err != nil {
		log.Printf("Migration completed, but failed to get version: %v", err)
	} else {
		log.Printf("Migration completed successfully. Current version: %d, Dirty: %t", versionCurrent, dirty)
	}
}
