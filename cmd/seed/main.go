package main

import (
	"flag"
	"fmt"
	"log"
	"math/rand"
	"time"

	"stellar-go/internal/config"
	"stellar-go/internal/database"
	"stellar-go/internal/models"

	"gorm.io/gorm"
)

var (
	sampleUsers = []models.User{
		{Email: "<EMAIL>", FirstName: "<PERSON>", LastName: "Do<PERSON>", Active: true, Admin: false},
		{Email: "<EMAIL>", FirstName: "<PERSON>", LastName: "<PERSON>", Active: true, Admin: true},
		{Email: "<EMAIL>", FirstName: "<PERSON>", LastName: "<PERSON>", Active: true, Admin: false},
		{Email: "<EMAIL>", FirstName: "<PERSON>", LastName: "<PERSON>", Active: true, Admin: false},
		{Email: "<EMAIL>", FirstName: "<PERSON>", LastName: "<PERSON>", Active: true, Admin: false},
		{Email: "<EMAIL>", FirstName: "<PERSON>", LastName: "<PERSON>", Active: true, Admin: false},
		{Email: "<EMAIL>", FirstName: "Eve", LastName: "<PERSON>", Active: true, Admin: false},
		{Email: "<EMAIL>", FirstName: "Frank", LastName: "Garcia", Active: true, Admin: false},
		{Email: "<EMAIL>", FirstName: "Grace", LastName: "Martinez", Active: true, Admin: false},
		{Email: "<EMAIL>", FirstName: "Henry", LastName: "Anderson", Active: true, Admin: false},
	}

	sampleDescriptions = []string{
		"Outstanding work on the quarterly presentation!",
		"Great collaboration during the project sprint.",
		"Excellent problem-solving skills demonstrated.",
		"Went above and beyond to help the team.",
		"Innovative approach to solving complex challenges.",
		"Reliable and consistent performance throughout the quarter.",
		"Authentic leadership during difficult times.",
		"Significant impact on our team's success.",
		"Proactive initiative in identifying process improvements.",
		"Continuous learning and self-development efforts.",
		"Creative solution that saved us time and resources.",
		"Dependable team player who always delivers.",
		"Honest feedback that helped improve our processes.",
		"Meaningful contribution to our company goals.",
		"Taking ownership and driving results.",
		"Sharing knowledge and mentoring others.",
	}
)

func main() {
	var (
		users    = flag.Int("users", 10, "Number of users to create")
		orbstars = flag.Int("orbstars", 50, "Number of orbstars to create")
		clean    = flag.Bool("clean", false, "Clean existing data before seeding")
	)
	flag.Parse()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	db, err := database.Initialize(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Clean existing data if requested
	if *clean {
		log.Println("Cleaning existing data...")
		if err := cleanData(db); err != nil {
			log.Fatalf("Failed to clean data: %v", err)
		}
	}

	// Seed users
	log.Printf("Creating %d users...", *users)
	createdUsers, err := seedUsers(db, *users)
	if err != nil {
		log.Fatalf("Failed to seed users: %v", err)
	}

	// Seed orbstars
	log.Printf("Creating %d orbstars...", *orbstars)
	if err := seedOrbstars(db, createdUsers, *orbstars); err != nil {
		log.Fatalf("Failed to seed orbstars: %v", err)
	}

	log.Println("Seeding completed successfully!")
}

func cleanData(db *gorm.DB) error {
	// Delete in order to respect foreign key constraints
	if err := db.Exec("DELETE FROM orbstar").Error; err != nil {
		return fmt.Errorf("failed to delete orbstars: %w", err)
	}

	if err := db.Exec("DELETE FROM userprofile").Error; err != nil {
		return fmt.Errorf("failed to delete users: %w", err)
	}

	// Reset sequences
	if err := db.Exec("ALTER SEQUENCE userprofile_id_seq RESTART WITH 1").Error; err != nil {
		return fmt.Errorf("failed to reset userprofile sequence: %w", err)
	}

	if err := db.Exec("ALTER SEQUENCE orbstar_id_seq RESTART WITH 1").Error; err != nil {
		return fmt.Errorf("failed to reset orbstar sequence: %w", err)
	}

	return nil
}

func seedUsers(db *gorm.DB, count int) ([]models.User, error) {
	var users []models.User

	// Use sample users first, then generate random ones
	for i := 0; i < count; i++ {
		var user models.User

		if i < len(sampleUsers) {
			user = sampleUsers[i]
		} else {
			user = models.User{
				Email:     fmt.Sprintf("<EMAIL>", i+1),
				FirstName: fmt.Sprintf("User%d", i+1),
				LastName:  fmt.Sprintf("LastName%d", i+1),
				Active:    true,
				Admin:     i == 1, // Make second user admin
			}
		}

		if err := db.Create(&user).Error; err != nil {
			return nil, fmt.Errorf("failed to create user %s: %w", user.Email, err)
		}

		users = append(users, user)
	}

	return users, nil
}

func seedOrbstars(db *gorm.DB, users []models.User, count int) error {
	if len(users) < 2 {
		return fmt.Errorf("need at least 2 users to create orbstars")
	}

	values := models.GetAllCompanyValues()

	for i := 0; i < count; i++ {
		// Select random giver and receiver (ensure they're different)
		giverIdx := rand.Intn(len(users))
		receiverIdx := rand.Intn(len(users))
		for receiverIdx == giverIdx {
			receiverIdx = rand.Intn(len(users))
		}

		giver := users[giverIdx]
		receiver := users[receiverIdx]

		// Select random value and description
		value := values[rand.Intn(len(values))]
		description := sampleDescriptions[rand.Intn(len(sampleDescriptions))]

		// Create orbstar with random timestamp in the past 30 days
		createdAt := time.Now().AddDate(0, 0, -rand.Intn(30))

		orbstar := models.Orbstar{
			GiverID:     giver.ID,
			ReceiverID:  receiver.ID,
			Value:       value,
			Description: description,
		}

		if err := db.Create(&orbstar).Error; err != nil {
			return fmt.Errorf("failed to create orbstar: %w", err)
		}

		// Update created_at to random past date
		if err := db.Model(&orbstar).Update("created_at", createdAt).Error; err != nil {
			return fmt.Errorf("failed to update orbstar created_at: %w", err)
		}
	}

	return nil
}
