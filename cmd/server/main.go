package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"stellar-go/internal/api"
	"stellar-go/internal/config"
	"stellar-go/internal/database"
	"stellar-go/internal/redis"
	"stellar-go/internal/sentry"

	"github.com/gin-gonic/gin"
)

// @title Stellar Backend API
// @version 1.0
// @description Stellar is a peer-to-peer recognition system that allows employees to give and receive "Orbstars" - digital recognition tokens based on company values.
// @termsOfService http://swagger.io/terms/

// @contact.name Byte Orbit
// @contact.url https://byteorbit.com
// @contact.email <EMAIL>

// @host localhost:8080
// @BasePath /

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize Sentry
	if err := sentry.Initialize(cfg); err != nil {
		log.Fatalf("Failed to initialize Sentry: %v", err)
	}

	// Initialize Redis (optional)
	if err := redis.InitFromConfig(cfg); err != nil {
		log.Printf("Failed to initialize Redis: %v", err)
	}

	// Initialize database
	db, err := database.Initialize(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Set Gin mode based on DEBUG flag (overrides environment)
	if cfg.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize router
	router := gin.New()

	// Configure trusted proxies (for X-Forwarded-* headers) if provided
	if cfg.TrustedProxies != "" {
		// Split comma-separated list into slice of CIDRs/IPs
		proxies := strings.Split(cfg.TrustedProxies, ",")
		for i := range proxies {
			proxies[i] = strings.TrimSpace(proxies[i])
		}
		if err := router.SetTrustedProxies(proxies); err != nil {
			log.Printf("warning: failed to set trusted proxies: %v", err)
		}
	}
	// Configure trusted platform header (e.g., 'X-Forwarded-For' for Traefik)
	if cfg.TrustedPlatform != "" {
		// When set, Gin will use this header for ClientIP and skip proxy checks
		router.TrustedPlatform = cfg.TrustedPlatform
	}

	// Always log HTTP requests; verbosity still controlled by Gin mode (Debug vs Release)
	router.Use(gin.Logger())
	router.Use(sentry.Middleware())
	router.Use(sentry.UserContextMiddleware())
	router.Use(gin.Recovery())

	// Setup API routes
	api.SetupRoutes(router, db, cfg)

	// Create HTTP server
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting server on port %d", cfg.Port)
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// Give outstanding requests 30 seconds to complete
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	// Flush Sentry events before exiting
	log.Println("Flushing Sentry events...")
	sentry.Flush(5 * time.Second)

	log.Println("Server exited")
}
