# Stellar Go API

A peer-to-peer recognition system that allows employees to give and receive "Orbstars" - digital recognition tokens based on company values. This is a Go port of the original Python Chalice application with enhanced features including SCIM support and Slack integration.

## Features

- 🌟 **Peer-to-peer recognition system** with company values
- 🔐 **Okta OIDC authentication** with JWT tokens
- 📡 **SCIM 2.0 support** for user provisioning from Okta
- 💬 **Slack integration** for notifications (replaces Mattermost)
- 📊 **Company values leaderboard** and statistics
- 🔍 **OpenAPI/Swagger documentation** with interactive UI
- 🐳 **Docker-first development** and deployment
- 🧪 **Comprehensive testing** with fixtures and test containers
- 📈 **Database migrations** with golang-migrate
- 🎯 **Admin functionality** for user and orbstar management

## Technology Stack

- **Language**: Go 1.21+
- **Framework**: Gin HTTP router
- **Database**: PostgreSQL 15+ with GORM ORM
- **Authentication**: Okta OIDC + JWT tokens (no Management API)
- **Documentation**: Swaggo for OpenAPI/Swagger
- **Migrations**: golang-migrate
- **Testing**: Testify + Testcontainers
- **Notifications**: Slack SDK
- **Containerization**: Docker + Docker Compose

## Quick Start

### Prerequisites

- Go 1.21 or later
- Docker and Docker Compose
- Okta developer account
- Slack workspace (optional)

### 1. Clone and Setup

```bash
git clone <repository-url>
cd stellar-go
```

### 2. Configure Environment

**Option A: Using Docker (Recommended for quick start)**
```bash
# Copy environment template
cp .env.example .env

# Edit .env with your values (or use development defaults)
# At minimum, you should change:
# - SECRET_KEY (use a strong random key)
# - OKTA_* variables (your Okta app credentials)
# - SCIM_BEARER_TOKEN (for user provisioning)
```

**Option B: Using environment variables directly**
```bash
export OKTA_DOMAIN="your-domain.okta.com"
export OKTA_CLIENT_ID="your-client-id"
export OKTA_CLIENT_SECRET="your-client-secret"
export SECRET_KEY="your-super-secret-jwt-key"
export SCIM_BEARER_TOKEN="your-scim-bearer-token"
export DB_PASSWORD="your-database-password"
# ... other variables
```

### 3. Start Supporting Services

The docker-compose setup provides the database and supporting services for local development:

```bash
# Start PostgreSQL, Redis, and Adminer
docker-compose up -d

# Check that services are running
docker-compose ps
```

This will start:
- **PostgreSQL** on port 5432 (database)
- **Redis** on port 6379 (caching)
- **Adminer** on port 8081 (database management UI)

### 4. Local Development (Recommended)

The docker-compose setup is now optimized for local development - it only runs the supporting services (PostgreSQL, Redis, Adminer) while you run the Go application directly on your machine.

```bash
# Start supporting services (database, cache, admin UI)
docker-compose up -d

# The .env file will be automatically loaded by docker-compose
# Run migrations and seed data
./scripts/dev.sh

# Start API locally (loads environment from .env file)
go run ./cmd/server
```

The API will be available at:
- **API**: http://localhost:8080
- **Swagger UI**: http://localhost:8080/swagger/index.html
- **Database UI**: http://localhost:8081 (Adminer)

## API Endpoints

### Authentication
- `GET /login` - Initiate Okta login
- `GET /login/callback` - Handle Okta callback
- `POST /auth/refresh` - Refresh JWT tokens

### Users
- `GET /user` - Get current user profile
- `GET /users` - List all users (with filtering)
- `GET /users/count` - Get user count
- `GET /users/:id` - Get user by ID (admin only)
- `PUT /users/:id` - Update user (admin only)

### Orbstars (Recognition)
- `GET /orbstars` - List all orbstars (paginated)
- `POST /orbstars` - Create new orbstar(s)
- `DELETE /orbstars` - Delete orbstar (admin only)
- `GET /orbstars/received` - Get user's received orbstars
- `GET /orbstars/given` - Get user's given orbstars
- `GET /orbstars/leaderboard` - Get company values leaderboard

### Company Values
- `GET /values` - Get list of company values

### SCIM 2.0 (User Provisioning)
- `GET /scim/v2/Users` - List users
- `GET /scim/v2/Users/<USER>
- `POST /scim/v2/Users` - Create user
- `PUT /scim/v2/Users/<USER>
- `DELETE /scim/v2/Users/<USER>

### Health & Monitoring
- `GET /` - Homepage
- `GET /healthz` - Health check (Kubernetes standard)
- `GET /fail` - Test error endpoint

## Database Management

### Migrations

```bash
# Run all migrations up
go run ./cmd/migrate -direction up

# Run specific number of migrations
go run ./cmd/migrate -direction up -steps 1

# Rollback migrations
go run ./cmd/migrate -direction down -steps 1

# Migrate to specific version
go run ./cmd/migrate -version 1
```

### Seed Data

```bash
# Create dummy data
go run ./cmd/seed -users 10 -orbstars 50

# Clean and recreate data
go run ./cmd/seed -clean -users 20 -orbstars 100
```

## Testing

### Run All Tests

```bash
./scripts/test.sh
```

### Run Specific Tests

```bash
# Unit tests only
go test -v ./internal/... -short

# Integration tests
go test -v ./tests/integration/...

# With coverage
go test -coverprofile=coverage.out ./internal/...
go tool cover -html=coverage.out -o coverage.html
```

## Configuration

### Configuration Loading

The application loads configuration in the following priority order (highest to lowest):

1. **Environment Variables** - Direct OS environment variables (highest priority)
2. **.env File** - Environment variables from `.env` file in the project root
3. **YAML Config File** - `config.yaml` file (for backward compatibility)
4. **Default Values** - Built-in defaults (lowest priority)

This means you can:
- Use `.env` file for local development
- Override specific values with environment variables
- Use YAML config for complex configurations

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `PORT` | Server port | No | 8080 |
| `ENVIRONMENT` | Environment (development/production) | No | development |
| `DB_HOST` | Database host | No | localhost |
| `DB_PORT` | Database port | No | 5432 |
| `DB_NAME` | Database name | No | stellar |
| `DB_USER` | Database user | No | stellar_user |
| `DB_PASSWORD` | Database password | **Yes** | - |
| `SECRET_KEY` | JWT secret key | **Yes** | - |
| `BACKEND_URL` | Backend URL for JWT issuer | **Yes** | - |
| `FRONTEND_URL` | Frontend URL for JWT audience | **Yes** | - |
| `OKTA_DOMAIN` | Okta domain | **Yes** | - |
| `OKTA_CLIENT_ID` | Okta client ID | **Yes** | - |
| `OKTA_CLIENT_SECRET` | Okta client secret | **Yes** | - |
| `OKTA_REDIRECT_URI` | Okta redirect URI | **Yes** | - |
| `IDP_ADMIN_GROUP` | Okta admin group name | No | - |
| `SCIM_BEARER_TOKEN` | SCIM authentication token | **Yes** | - |
| `SLACK_TOKEN` | Slack bot token | No | - |
| `SLACK_NOTIFICATION_CHANNEL_ID` | Slack channel for notifications | No | - |

## Docker Deployment

### Development

```bash
docker-compose up -d
```

### Production

```bash
# Build production image
docker build -t stellar-api:latest .

# Run with production configuration
docker run -d \
  --name stellar-api \
  -p 8080:8080 \
  -e DB_PASSWORD=your-password \
  -e SECRET_KEY=your-secret \
  # ... other environment variables
  stellar-api:latest
```

## Company Values

The system tracks six core company values:

1. **Self Development** 📚 - Personal growth and learning
2. **Innovation** 💡 - Creative problem-solving and new ideas
3. **Initiative** 🚀 - Proactive behavior and leadership
4. **Authenticity** 💯 - Genuine and honest interactions
5. **Impact** 🎯 - Meaningful contributions to goals
6. **Reliability** ⚡ - Consistent and dependable performance

## Documentation

For detailed setup and configuration guides, see the [docs](docs/) directory:

- **[Okta Integration Setup](docs/okta-setup.md)** - Complete guide for OIDC authentication and SCIM 2.0 provisioning
- **[Documentation Index](docs/README.md)** - Overview of all available documentation

## Quick Setup - Okta Integration

For a complete setup guide, see [docs/okta-setup.md](docs/okta-setup.md). Quick overview:

1. Create Okta OIDC web application
2. Configure group claims for admin access
3. Set up SCIM 2.0 application for user provisioning
4. Update environment variables with Okta credentials

## Slack Integration

1. Create a Slack app in your workspace
2. Add the following OAuth scopes:
   - `chat:write`
   - `users:read`
   - `users:read.email`
3. Install the app to your workspace
4. Set `SLACK_TOKEN` to your bot token
5. Optionally set `SLACK_NOTIFICATION_CHANNEL_ID` for channel notifications

## Swagger

To update the Swagger / OpenAPI documentation, run:
```bash
swag init -g cmd/server/main.go -o docs
```

## Troubleshooting

### Common Issues

1. **"DB_PASSWORD is required" Error**
   ```bash
   # Make sure .env file exists with DB_PASSWORD set
   cp .env.example .env
   # Edit .env and set DB_PASSWORD=stellar_password (or your password)

   # Or set environment variable directly
   export DB_PASSWORD=stellar_password
   docker-compose up -d
   ```

2. **Database Connection Failed**
   - Check `DB_*` environment variables in `.env` file
   - Ensure PostgreSQL container is running: `docker-compose ps`
   - Check PostgreSQL logs: `docker-compose logs postgres`
   - Verify network connectivity between containers

3. **Go Application Won't Start**
   ```bash
   # Check that supporting services are running
   docker-compose ps

   # Check database connectivity
   docker-compose logs postgres

   # Common fixes:
   # - Ensure .env file exists and has all required variables
   # - Wait for PostgreSQL to be fully ready before starting Go app
   # - Check if port 8080 is already in use
   ```

4. **Okta Authentication Failed**
   - Verify `OKTA_*` configuration in `.env`
   - Check redirect URIs match exactly
   - Ensure user has access to the application
   - For development, you can use placeholder values

5. **SCIM Provisioning Failed**
   - Verify `SCIM_BEARER_TOKEN` in `.env`
   - Check SCIM endpoint URL in Okta
   - Review Okta provisioning logs

6. **Slack Notifications Not Working**
   - Verify `SLACK_TOKEN` is valid
   - Check bot permissions
   - Ensure users exist in Slack workspace
   - Leave `SLACK_TOKEN` empty to disable notifications

### Debug Commands

```bash
# Check all container status
docker-compose ps

# View all logs
docker-compose logs

# View specific service logs
docker-compose logs postgres
docker-compose logs redis

# Restart a specific service
docker-compose restart postgres

# Clean restart (removes volumes)
docker-compose down -v && docker-compose up -d

# Test database connection
docker-compose exec postgres psql -U stellar_user -d stellar -c "SELECT 1;"

# Test API health
curl http://localhost:8080/healthz
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

