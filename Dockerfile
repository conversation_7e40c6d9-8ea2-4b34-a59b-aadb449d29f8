# syntax=docker/dockerfile:1.7

# Build stage
FROM --platform=$BUILDPLATFORM golang:1.24-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go module files first for better caching
COPY go.mod go.sum ./

# Download dependencies using BuildKit cache mounts
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download

# Copy only Go source (avoids invalidating deps when static/migrations change)
COPY internal/ ./internal/
COPY pkg/ ./pkg/
COPY cmd/ ./cmd/
# Swagger docs package needed by router blank import
COPY docs/ ./docs/

# Build binaries with Go build cache mounts
ENV CGO_ENABLED=0 GOOS=linux
RUN --mount=type=cache,target=/root/.cache/go-build --mount=type=cache,target=/go/pkg/mod \
    go build -trimpath -ldflags "-s -w" -o stellar-server ./cmd/server
RUN --mount=type=cache,target=/root/.cache/go-build --mount=type=cache,target=/go/pkg/mod \
    go build -trimpath -ldflags "-s -w" -o stellar-migrate ./cmd/migrate
RUN --mount=type=cache,target=/root/.cache/go-build --mount=type=cache,target=/go/pkg/mod \
    go build -trimpath -ldflags "-s -w" -o stellar-seed ./cmd/seed

# Final stage
FROM alpine:3.20

# Install runtime dependencies
RUN apk --no-cache add ca-certificates curl

# Create app user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set working directory
WORKDIR /app

# Copy binaries from builder with correct ownership
COPY --from=builder --chown=appuser:appgroup /app/stellar-server .
COPY --from=builder --chown=appuser:appgroup /app/stellar-migrate .
COPY --from=builder --chown=appuser:appgroup /app/stellar-seed .

# Copy runtime assets from context separately so Go build cache isn't busted
COPY --chown=appuser:appgroup migrations ./migrations
COPY --chown=appuser:appgroup static ./static

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# Default command
CMD ["./stellar-server"]
