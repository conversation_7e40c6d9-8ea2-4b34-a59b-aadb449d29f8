package slack

import (
	"fmt"
	"log"
	"strings"

	"stellar-go/internal/config"
	"stellar-go/internal/models"

	"github.com/slack-go/slack"
)

// Client wraps the Slack API client
type Client struct {
	api       *slack.Client
	channelID string
}

// NewClient creates a new Slack client
func NewClient(cfg *config.Config) *Client {
	if cfg.Slack.Token == "" {
		log.Println("Slack token not configured, notifications will be disabled")
		return nil
	}

	api := slack.New(cfg.Slack.Token)

	return &Client{
		api:       api,
		channelID: cfg.Slack.ChannelID,
	}
}

// SendOrbstarNotification sends a notification when an orbstar is created
// Deprecated: Use SendOrbstarDirectMessage and SendOrbstarChannelNotification instead
func (c *Client) SendOrbstarNotification(orbstar *models.Orbstar) error {
	if c == nil || c.api == nil {
		return nil // Slack not configured
	}

	// Send direct message to receiver
	if err := c.sendDirectMessage(orbstar); err != nil {
		log.Printf("Failed to send direct message: %v", err)
	}

	// Send to notification channel if configured
	if c.channelID != "" {
		if err := c.sendChannelMessage(orbstar); err != nil {
			log.Printf("Failed to send channel message: %v", err)
		}
	}

	return nil
}

// SendOrbstarDirectMessage sends a direct message notification to the orbstar receiver
func (c *Client) SendOrbstarDirectMessage(orbstar *models.Orbstar) error {
	if c == nil || c.api == nil {
		return nil // Slack not configured
	}

	return c.sendDirectMessage(orbstar)
}

// SendOrbstarChannelNotification sends a consolidated channel notification for one or more orbstars
func (c *Client) SendOrbstarChannelNotification(orbstars []models.Orbstar) error {
	if c == nil || c.api == nil || c.channelID == "" || len(orbstars) == 0 {
		return nil // Slack not configured or no orbstars
	}

	// If only one orbstar, use the existing single message format
	if len(orbstars) == 1 {
		return c.sendChannelMessage(&orbstars[0])
	}

	// For multiple orbstars, create a consolidated message
	return c.sendMultiRecipientChannelMessage(orbstars)
}

// sendDirectMessage sends a direct message to the orbstar receiver
func (c *Client) sendDirectMessage(orbstar *models.Orbstar) error {
	// Find user by email
	user, err := c.api.GetUserByEmail(orbstar.Receiver.Email)
	if err != nil {
		return fmt.Errorf("failed to find Slack user by email %s: %w", orbstar.Receiver.Email, err)
	}

	// Create message blocks
	blocks := c.createOrbstarMessageBlocks(orbstar, true)

	// Send direct message
	_, _, err = c.api.PostMessage(
		user.ID,
		slack.MsgOptionBlocks(blocks...),
		slack.MsgOptionAsUser(false),
	)
	if err != nil {
		return fmt.Errorf("failed to send direct message: %w", err)
	}

	return nil
}

// sendChannelMessage sends a message to the notification channel
func (c *Client) sendChannelMessage(orbstar *models.Orbstar) error {
	// Create message blocks
	blocks := c.createOrbstarMessageBlocks(orbstar, false)

	// Send channel message
	_, _, err := c.api.PostMessage(
		c.channelID,
		slack.MsgOptionBlocks(blocks...),
		slack.MsgOptionAsUser(false),
	)
	if err != nil {
		return fmt.Errorf("failed to send channel message: %w", err)
	}

	return nil
}

// createOrbstarMessageBlocks creates Slack message blocks for orbstar notifications
func (c *Client) createOrbstarMessageBlocks(orbstar *models.Orbstar, isDM bool) []slack.Block {
	const maxTextLength = 12000 // limit per block to stay well under Slack's total limit

	var headerText string
	if isDM {
		headerText = "🌟 An Orbstar has entered your universe!"
	} else {
		headerText = fmt.Sprintf("🌟 %s received an Orbstar!", orbstar.Receiver.GetFullName())
	}

	// Header block
	headerBlock := slack.NewHeaderBlock(
		slack.NewTextBlockObject(slack.PlainTextType, headerText, false, false),
	)

	// Main content block
	var contentText string
	if isDM {
		contentText = fmt.Sprintf("*%s* gave you an Orbstar for *%s*",
			orbstar.Giver.GetFullName(),
			orbstar.Value,
		)
	} else {
		contentText = fmt.Sprintf("*%s* gave *%s* an Orbstar for *%s*",
			orbstar.Giver.GetFullName(),
			orbstar.Receiver.GetFullName(),
			orbstar.Value,
		)
	}

	contentBlock := slack.NewSectionBlock(
		slack.NewTextBlockObject(slack.MarkdownType, contentText, false, false),
		nil, nil,
	)

	// Quote block for the description - format as proper blockquote
	quoteBlock := createQuoteBlock(orbstar.Description, maxTextLength)

	// Divider
	dividerBlock := slack.NewDividerBlock()

	// Company value context
	valueEmoji := getValueEmoji(orbstar.Value)
	contextText := fmt.Sprintf("%s *%s* - %s",
		valueEmoji,
		orbstar.Value,
		getValueDescription(orbstar.Value),
	)

	contextBlock := slack.NewContextBlock(
		"",
		[]slack.MixedElement{
			slack.NewTextBlockObject(slack.MarkdownType, contextText, false, false),
		}...,
	)

	return []slack.Block{
		headerBlock,
		contentBlock,
		quoteBlock,
		dividerBlock,
		contextBlock,
	}
}

// getValueEmoji returns an emoji for each company value
func getValueEmoji(value models.CompanyValue) string {
	switch value {
	case models.SelfDevelopment:
		return "📚"
	case models.Innovation:
		return "💡"
	case models.Initiative:
		return "🚀"
	case models.Authenticity:
		return "💯"
	case models.Impact:
		return "🎯"
	case models.Reliability:
		return "⚡"
	default:
		return "⭐"
	}
}

// getValueDescription returns a description for each company value
func getValueDescription(value models.CompanyValue) string {
	switch value {
	case models.SelfDevelopment:
		return "Personal growth and learning"
	case models.Innovation:
		return "Creative problem-solving and new ideas"
	case models.Initiative:
		return "Proactive behavior and leadership"
	case models.Authenticity:
		return "Genuine and honest interactions"
	case models.Impact:
		return "Meaningful contributions to goals"
	case models.Reliability:
		return "Consistent and dependable performance"
	default:
		return "Company value recognition"
	}
}

// TestConnection tests the Slack API connection
func (c *Client) TestConnection() error {
	if c == nil || c.api == nil {
		return fmt.Errorf("slack client not configured")
	}

	_, err := c.api.AuthTest()
	if err != nil {
		return fmt.Errorf("slack authentication failed: %w", err)
	}

	return nil
}

// GetChannelInfo retrieves information about the notification channel
func (c *Client) GetChannelInfo() (*slack.Channel, error) {
	if c == nil || c.api == nil {
		return nil, fmt.Errorf("slack client not configured")
	}

	if c.channelID == "" {
		return nil, fmt.Errorf("notification channel not configured")
	}

	channel, err := c.api.GetConversationInfo(&slack.GetConversationInfoInput{
		ChannelID: c.channelID,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get channel info: %w", err)
	}

	return channel, nil
}

// SendTestMessage sends a test message to verify Slack integration
func (c *Client) SendTestMessage() error {
	if c == nil || c.api == nil {
		return fmt.Errorf("slack client not configured")
	}

	if c.channelID == "" {
		return fmt.Errorf("notification channel not configured")
	}

	blocks := []slack.Block{
		slack.NewHeaderBlock(
			slack.NewTextBlockObject(slack.PlainTextType, "🧪 Stellar API Test Message", false, false),
		),
		slack.NewSectionBlock(
			slack.NewTextBlockObject(slack.MarkdownType, "This is a test message from the Stellar API to verify Slack integration is working correctly.", false, false),
			nil, nil,
		),
	}

	_, _, err := c.api.PostMessage(
		c.channelID,
		slack.MsgOptionBlocks(blocks...),
		slack.MsgOptionAsUser(false),
	)
	if err != nil {
		return fmt.Errorf("failed to send test message: %w", err)
	}

	return nil
}

// truncateText truncates text to maxLength and adds ellipsis if truncated
func truncateText(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}

	if maxLength <= 3 {
		return "..."
	}

	return text[:maxLength-3] + "..."
}

// createQuoteBlock creates a properly formatted blockquote block for Slack
func createQuoteBlock(description string, maxTextLength int) slack.Block {
	// Calculate the overhead that will be added by quote formatting
	overhead := calculateQuoteOverhead(description)

	// Truncate based on the actual overhead needed
	maxContentLength := maxTextLength - overhead
	if maxContentLength < 0 {
		maxContentLength = 100 // Minimum reasonable length
	}

	truncatedDescription := truncateText(description, maxContentLength)

	// Split by newlines and add '> ' to each line
	lines := strings.Split(truncatedDescription, "\n")
	quotedLines := make([]string, len(lines))

	for i, line := range lines {
		// Trim whitespace and add quote prefix
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "" {
			quotedLines[i] = ">" // Empty quoted line (+1 char)
		} else {
			quotedLines[i] = "> " + trimmedLine // Non-empty quoted line (+2 chars)
		}
	}

	quotedText := strings.Join(quotedLines, "\n")

	return slack.NewSectionBlock(
		slack.NewTextBlockObject(slack.MarkdownType, quotedText, false, false),
		nil, nil,
	)
}

// calculateQuoteOverhead calculates how many additional characters will be added
// when formatting text as a blockquote
func calculateQuoteOverhead(text string) int {
	if text == "" {
		return 2 // "> " for empty text
	}

	lines := strings.Split(text, "\n")
	overhead := 0

	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "" {
			overhead += 1 // ">" for empty lines
		} else {
			overhead += 2 // "> " for non-empty lines
		}
	}

	return overhead
}

// sendMultiRecipientChannelMessage sends a consolidated channel message for multiple orbstar recipients
func (c *Client) sendMultiRecipientChannelMessage(orbstars []models.Orbstar) error {
	if len(orbstars) == 0 {
		return nil
	}

	// All orbstars should have the same giver, value, and description since they're from the same request
	firstOrbstar := orbstars[0]

	// Create recipient list
	var recipients []string
	for _, orbstar := range orbstars {
		recipients = append(recipients, orbstar.Receiver.GetFullName())
	}

	// Create message blocks for multiple recipients
	blocks := c.createMultiRecipientMessageBlocks(&firstOrbstar, recipients)

	// Send channel message
	_, _, err := c.api.PostMessage(
		c.channelID,
		slack.MsgOptionBlocks(blocks...),
		slack.MsgOptionAsUser(false),
	)
	if err != nil {
		return fmt.Errorf("failed to send multi-recipient channel message: %w", err)
	}

	return nil
}

// createMultiRecipientMessageBlocks creates Slack message blocks for multi-recipient orbstar notifications
func (c *Client) createMultiRecipientMessageBlocks(orbstar *models.Orbstar, recipients []string) []slack.Block {
	const maxTextLength = 12000 // limit per block to stay well under Slack's total limit

	// Create header text for multiple recipients
	var headerText string
	if len(recipients) == 2 {
		headerText = fmt.Sprintf("🌟 %s and %s received Orbstars!", recipients[0], recipients[1])
	} else if len(recipients) > 2 {
		lastRecipient := recipients[len(recipients)-1]
		otherRecipients := strings.Join(recipients[:len(recipients)-1], ", ")
		headerText = fmt.Sprintf("🌟 %s, and %s received Orbstars!", otherRecipients, lastRecipient)
	} else {
		headerText = fmt.Sprintf("🌟 %s received an Orbstar!", recipients[0])
	}

	// Header block
	headerBlock := slack.NewHeaderBlock(
		slack.NewTextBlockObject(slack.PlainTextType, headerText, false, false),
	)

	// Main content block
	contentText := fmt.Sprintf("*%s* gave %s Orbstars for *%s*",
		orbstar.Giver.GetFullName(),
		func() string {
			if len(recipients) == 1 {
				return "an"
			}
			return fmt.Sprintf("%d", len(recipients))
		}(),
		orbstar.Value,
	)

	contentBlock := slack.NewSectionBlock(
		slack.NewTextBlockObject(slack.MarkdownType, contentText, false, false),
		nil, nil,
	)

	// Quote block for the description - format as proper blockquote
	quoteBlock := createQuoteBlock(orbstar.Description, maxTextLength)

	// Divider
	dividerBlock := slack.NewDividerBlock()

	// Company value context
	valueEmoji := getValueEmoji(orbstar.Value)
	contextText := fmt.Sprintf("%s *%s* - %s",
		valueEmoji,
		orbstar.Value,
		getValueDescription(orbstar.Value),
	)

	contextBlock := slack.NewContextBlock(
		"",
		[]slack.MixedElement{
			slack.NewTextBlockObject(slack.MarkdownType, contextText, false, false),
		}...,
	)

	return []slack.Block{
		headerBlock,
		contentBlock,
		quoteBlock,
		dividerBlock,
		contextBlock,
	}
}
