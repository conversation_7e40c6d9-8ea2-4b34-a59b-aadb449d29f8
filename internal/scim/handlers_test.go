package scim

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"stellar-go/internal/config"
	"stellar-go/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestSCIMAuthMiddleware(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		SCIM: config.SCIMConfig{
			BearerToken: "test-token-123",
		},
	}

	handler := &SCIMHandler{
		userService: &services.UserService{}, // Mock service
		config:      cfg,
	}

	tests := []struct {
		name           string
		authHeader     string
		expectedStatus int
		description    string
	}{
		{
			name:           "Valid Bearer token format",
			authHeader:     "Bearer test-token-123",
			expectedStatus: http.StatusOK,
			description:    "Standard Bearer token format should work",
		},
		{
			name:           "Valid raw token format (Okta SCIM)",
			authHeader:     "test-token-123",
			expectedStatus: http.StatusOK,
			description:    "Raw token format (as sent by Okta SCIM) should work",
		},
		{
			name:           "Missing authorization header",
			authHeader:     "",
			expectedStatus: http.StatusUnauthorized,
			description:    "Missing auth header should return 401",
		},
		{
			name:           "Invalid Bearer token",
			authHeader:     "Bearer wrong-token",
			expectedStatus: http.StatusUnauthorized,
			description:    "Wrong Bearer token should return 401",
		},
		{
			name:           "Invalid raw token",
			authHeader:     "wrong-token",
			expectedStatus: http.StatusUnauthorized,
			description:    "Wrong raw token should return 401",
		},
		{
			name:           "Invalid format with multiple spaces",
			authHeader:     "Bearer token with spaces",
			expectedStatus: http.StatusUnauthorized,
			description:    "Invalid format should return 401",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test router
			router := gin.New()

			// Add the auth middleware
			router.Use(handler.AuthMiddleware())

			// Add a test endpoint
			router.GET("/test", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{"message": "success"})
			})

			// Create test request
			req := httptest.NewRequest("GET", "/test", nil)
			if tt.authHeader != "" {
				req.Header.Set("Authorization", tt.authHeader)
			}

			// Record response
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, w.Code, tt.description)

			// For successful cases, check response body
			if tt.expectedStatus == http.StatusOK {
				assert.Contains(t, w.Body.String(), "success")
			}
		})
	}
}

func TestSCIMAuthMiddleware_Integration(t *testing.T) {
	// This test simulates the exact scenario from the Ngrok request
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		SCIM: config.SCIMConfig{
			BearerToken: "dev-scim-bearer-token-12345",
		},
	}

	handler := &SCIMHandler{
		userService: &services.UserService{}, // Mock service
		config:      cfg,
	}

	// Create test router
	router := gin.New()
	router.Use(handler.AuthMiddleware())

	// Add a mock SCIM endpoint
	router.GET("/scim/v2/Users", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"schemas":      []string{"urn:ietf:params:scim:api:messages:2.0:ListResponse"},
			"totalResults": 0,
			"startIndex":   1,
			"itemsPerPage": 0,
			"Resources":    []interface{}{},
		})
	})

	// Test the exact authorization header format from Okta
	req := httptest.NewRequest("GET", "/scim/v2/Users?startIndex=1&count=2", nil)
	req.Header.Set("Authorization", "dev-scim-bearer-token-12345") // Raw token as sent by Okta
	req.Header.Set("Accept", "application/scim+json")
	req.Header.Set("User-Agent", "Okta SCIM Client 1.0.0")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should now return 200 instead of 401
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), "ListResponse")
}

func TestParseSCIMFilter(t *testing.T) {
	handler := &SCIMHandler{}

	tests := []struct {
		name     string
		filter   string
		expected string
	}{
		{
			name:     "Valid userName eq filter",
			filter:   `userName eq "<EMAIL>"`,
			expected: "<EMAIL>",
		},
		{
			name:     "Valid userName eq filter with spaces",
			filter:   ` userName eq "<EMAIL>" `,
			expected: "<EMAIL>",
		},
		{
			name:     "Case insensitive userName",
			filter:   `USERNAME eq "<EMAIL>"`,
			expected: "<EMAIL>",
		},
		{
			name:     "Invalid filter format",
			filter:   `userName ne "<EMAIL>"`,
			expected: "",
		},
		{
			name:     "Empty filter",
			filter:   "",
			expected: "",
		},
		{
			name:     "Filter without quotes",
			filter:   `<NAME_EMAIL>`,
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := handler.parseSCIMFilter(tt.filter)
			assert.Equal(t, tt.expected, result)
		})
	}
}
