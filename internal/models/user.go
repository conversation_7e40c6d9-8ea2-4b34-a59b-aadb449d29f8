package models

import (
	"time"

	"gorm.io/gorm"
)

// User represents a user in the system
type User struct {
	ID        uint      `json:"id" gorm:"primaryKey" example:"123"`
	Email     string    `json:"email" gorm:"uniqueIndex;size:64;not null" validate:"required,email,max=64" example:"<EMAIL>"`
	FirstName string    `json:"first_name" gorm:"size:24;not null" validate:"required,max=24" example:"John"`
	LastName  string    `json:"last_name" gorm:"size:24;not null" validate:"required,max=24" example:"Doe"`
	Active    bool      `json:"active" gorm:"column:active;default:true;not null" example:"true"`
	Admin     bool      `json:"admin" gorm:"default:false;not null" example:"false"`
	CreatedAt time.Time `json:"-" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"-" gorm:"autoUpdateTime"`

	// Relationships
	GivenOrbstars    []Orbstar `json:"-" gorm:"foreignKey:GiverID"`
	ReceivedOrbstars []Orbstar `json:"-" gorm:"foreignKey:ReceiverID"`
}

// UserProfile extends User with additional profile information
type UserProfile struct {
	User
}

// UserSummary is a simplified user representation for API responses
type UserSummary struct {
	Email     string `json:"email" example:"<EMAIL>"`
	FirstName string `json:"first_name" example:"John"`
	LastName  string `json:"last_name" example:"Doe"`
}

// CreateUserRequest represents the request to create a new user
type CreateUserRequest struct {
	Email     string `json:"email" validate:"required,email,max=64" example:"<EMAIL>"`
	FirstName string `json:"first_name" validate:"required,max=24" example:"John"`
	LastName  string `json:"last_name" validate:"required,max=24" example:"Doe"`
	Active    *bool  `json:"active,omitempty" example:"true"`
	Admin     *bool  `json:"admin,omitempty" example:"false"`
}

// UpdateUserRequest represents the request to update a user
type UpdateUserRequest struct {
	FirstName *string `json:"first_name,omitempty" validate:"omitempty,max=24" example:"John"`
	LastName  *string `json:"last_name,omitempty" validate:"omitempty,max=24" example:"Doe"`
	Active    *bool   `json:"active,omitempty" example:"true"`
	Admin     *bool   `json:"admin,omitempty" example:"false"`
}

// UserListFilter represents filters for user list queries
type UserListFilter struct {
	FirstName string `form:"first_name" example:"John"`
	LastName  string `form:"last_name" example:"Doe"`
	Email     string `form:"email" example:"john"`
}

// TableName returns the table name for the User model
func (*User) TableName() string {
	return "userprofile"
}

// ToSummary converts a User to a UserSummary
func (u *User) ToSummary() UserSummary {
	return UserSummary{
		Email:     u.Email,
		FirstName: u.FirstName,
		LastName:  u.LastName,
	}
}

// BeforeCreate is a GORM hook that runs before creating a user
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// Set default values if not provided
	if u.Admin == false {
		u.Admin = false
	}
	// Note: We don't set a default for Active here since it should be explicitly set
	// The database default is true, but SCIM might want to create inactive users
	return nil
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	return u.FirstName + " " + u.LastName
}

// IsActive returns whether the user is active for authentication and visibility
func (u *User) IsActive() bool {
	return u.Active
}

// IsAdmin returns whether the user has admin privileges
func (u *User) IsAdmin() bool {
	return u.Admin
}
