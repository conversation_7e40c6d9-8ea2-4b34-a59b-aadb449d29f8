package models

import (
	"time"

	"gorm.io/gorm"
)

// RefreshTokenBlacklist represents a blacklisted refresh token
type RefreshTokenBlacklist struct {
	ID            uint      `json:"id" gorm:"primaryKey"`
	TokenID       string    `json:"token_id" gorm:"uniqueIndex;size:255;not null"`
	UserID        uint      `json:"user_id" gorm:"not null"`
	BlacklistedAt time.Time `json:"blacklisted_at" gorm:"autoCreateTime"`
	ExpiresAt     time.Time `json:"expires_at" gorm:"not null"`

	// Relationships
	User User `json:"-" gorm:"foreignKey:UserID"`
}

// TableName returns the table name for the RefreshTokenBlacklist model
func (*RefreshTokenBlacklist) TableName() string {
	return "refresh_token_blacklist"
}

// IsExpired checks if the blacklist entry is expired
func (rtb *RefreshTokenBlacklist) IsExpired() bool {
	return time.Now().After(rtb.ExpiresAt)
}

// BeforeCreate is a GORM hook that runs before creating a blacklist entry
func (rtb *RefreshTokenBlacklist) BeforeCreate(tx *gorm.DB) error {
	// If ExpiresAt is not set, set it to 2 hours from now (default refresh token lifetime)
	if rtb.ExpiresAt.IsZero() {
		rtb.ExpiresAt = time.Now().Add(2 * time.Hour)
	}
	return nil
}
