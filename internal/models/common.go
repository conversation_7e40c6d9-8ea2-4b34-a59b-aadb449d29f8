package models

// PaginatedResponse represents the common pagination metadata
type PaginatedResponse struct {
	Page    int  `json:"page" example:"1"`
	HasNext bool `json:"has_next" example:"true"`
	HasPrev bool `json:"has_prev" example:"false"`
	Count   int  `json:"count" example:"42"`
}

// ErrorResponse represents a standard error response
type ErrorResponse struct {
	Error string `json:"error" example:"Invalid token payload"`
	Code  string `json:"code,omitempty" example:"invalid_token"`
}

// ValidationErrorResponse represents validation errors
type ValidationErrorResponse struct {
	Errors map[string]interface{} `json:"errors" example:"{\"receiver\": [\"Receiver with id 999 does not exist\"], \"description\": [\"This field is required\"]}"`
}

// SuccessResponse represents a generic success response
type SuccessResponse struct {
	Status string `json:"status" example:"Ok"`
}

// HealthStatus represents the status of a service component
type HealthStatus string

const (
	HealthStatusHealthy   HealthStatus = "healthy"
	HealthStatusUnhealthy HealthStatus = "unhealthy"
)

// ComponentHealth represents the health status of a single component
type ComponentHealth struct {
	Status HealthStatus `json:"status" example:"healthy"`
	Error  string       `json:"error,omitempty" example:"connection timeout"`
}

// HealthResponse represents the overall health check response
type HealthResponse struct {
	Status   HealthStatus               `json:"status" example:"healthy"`
	Message  string                     `json:"message" example:"Stellar | Be lekker"`
	Services map[string]ComponentHealth `json:"services"`
}

// TokenResponse represents JWT token response
type TokenResponse struct {
	Access  string `json:"access" example:"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."`
	Refresh string `json:"refresh" example:"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."`
}

// RefreshTokenRequest represents the request to refresh tokens
type RefreshTokenRequest struct {
	Access  string `json:"access" validate:"required" example:"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."`
	Refresh string `json:"refresh" validate:"required" example:"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."`
}

// LogoutRequest represents the request to logout
type LogoutRequest struct {
	Refresh string `json:"refresh" validate:"required" example:"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."`
}

// SessionExchangeRequest represents the request to exchange a session for tokens
type SessionExchangeRequest struct {
	SessionID string `json:"session_id" validate:"required" example:"abc123def456ghi789"`
}

// SessionExchangeResponse represents the response from session exchange (same as TokenResponse)
type SessionExchangeResponse struct {
	Access  string `json:"access" example:"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."`
	Refresh string `json:"refresh" example:"eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."`
}

// PaginationParams represents common pagination parameters
type PaginationParams struct {
	Page int `form:"page" example:"1" default:"1" minimum:"1"`
}

// GetOffset calculates the offset for database queries
func (p *PaginationParams) GetOffset(pageSize int) int {
	if p.Page <= 1 {
		return 0
	}
	return (p.Page - 1) * pageSize
}

// GetPage returns the page number, defaulting to 1 if invalid
func (p *PaginationParams) GetPage() int {
	if p.Page <= 0 {
		return 1
	}
	return p.Page
}

// CalculatePagination calculates pagination metadata
func CalculatePagination(page, pageSize, totalCount int) PaginatedResponse {
	if page <= 0 {
		page = 1
	}

	totalPages := (totalCount + pageSize - 1) / pageSize
	if totalPages == 0 {
		totalPages = 1
	}

	return PaginatedResponse{
		Page:    page,
		HasNext: page < totalPages,
		HasPrev: page > 1,
		Count:   totalCount,
	}
}
