package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestCompanyValue_IsValid(t *testing.T) {
	tests := []struct {
		name     string
		value    CompanyValue
		expected bool
	}{
		{"valid - Self Development", SelfDevelopment, true},
		{"valid - Innovation", Innovation, true},
		{"valid - Initiative", Initiative, true},
		{"valid - Authenticity", Authenticity, true},
		{"valid - Impact", Impact, true},
		{"valid - Reliability", Reliability, true},
		{"invalid value", CompanyValue("Invalid"), false},
		{"empty value", CompanyValue(""), false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.value.IsValid())
		})
	}
}

func TestGetAllCompanyValues(t *testing.T) {
	values := GetAllCompanyValues()

	assert.Len(t, values, 6)
	assert.Contains(t, values, SelfDevelopment)
	assert.Contains(t, values, Innovation)
	assert.Contains(t, values, Initiative)
	assert.Contains(t, values, Authenticity)
	assert.Contains(t, values, Impact)
	assert.Contains(t, values, Reliability)
}

func TestOrbstar_ToResponse(t *testing.T) {
	giver := User{
		ID:        1,
		Email:     "<EMAIL>",
		FirstName: "Giver",
		LastName:  "User",
	}

	receiver := User{
		ID:        2,
		Email:     "<EMAIL>",
		FirstName: "Receiver",
		LastName:  "User",
	}

	orbstar := Orbstar{
		ID:          123,
		Value:       Innovation,
		Description: "Great work!",
		GiverID:     giver.ID,
		ReceiverID:  receiver.ID,
		Giver:       giver,
		Receiver:    receiver,
	}

	response := orbstar.ToResponse()

	assert.Equal(t, uint(123), response.ID)
	assert.Equal(t, "Innovation", response.Value)
	assert.Equal(t, "Great work!", response.Description)
	assert.Equal(t, "<EMAIL>", response.Giver.Email)
	assert.Equal(t, "<EMAIL>", response.Receiver.Email)
}

func TestCreateOrbstarRequest_GetReceiverIDs(t *testing.T) {
	tests := []struct {
		name        string
		receiver    interface{}
		expected    []uint
		expectError bool
	}{
		{
			name:        "single receiver",
			receiver:    float64(123),
			expected:    []uint{123},
			expectError: false,
		},
		{
			name:        "multiple receivers",
			receiver:    []interface{}{float64(123), float64(456)},
			expected:    []uint{123, 456},
			expectError: false,
		},
		{
			name:        "too many receivers",
			receiver:    []interface{}{float64(1), float64(2), float64(3), float64(4), float64(5), float64(6)},
			expected:    nil,
			expectError: true,
		},
		{
			name:        "invalid type",
			receiver:    "invalid",
			expected:    nil,
			expectError: true,
		},
		{
			name:        "empty array",
			receiver:    []interface{}{},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := CreateOrbstarRequest{
				Receiver: tt.receiver,
			}

			result, err := req.GetReceiverIDs()

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestGetValuesResponse(t *testing.T) {
	response := GetValuesResponse()

	assert.Len(t, response.Values, 6)
	assert.Contains(t, response.Values, "Self Development")
	assert.Contains(t, response.Values, "Innovation")
	assert.Contains(t, response.Values, "Initiative")
	assert.Contains(t, response.Values, "Authenticity")
	assert.Contains(t, response.Values, "Impact")
	assert.Contains(t, response.Values, "Reliability")
}
