package models

import (
	"time"

	"gorm.io/gorm"
)

// CompanyValue represents the company values enum
type CompanyValue string

const (
	SelfDevelopment CompanyValue = "Self Development"
	Innovation      CompanyValue = "Innovation"
	Initiative      CompanyValue = "Initiative"
	Authenticity    CompanyValue = "Authenticity"
	Impact          CompanyValue = "Impact"
	Reliability     CompanyValue = "Reliability"
)

// GetAllCompanyValues returns all available company values
func GetAllCompanyValues() []CompanyValue {
	return []CompanyValue{
		SelfDevelopment,
		Innovation,
		Initiative,
		Authenticity,
		Impact,
		Reliability,
	}
}

// IsValid checks if the company value is valid
func (cv CompanyValue) IsValid() bool {
	for _, v := range GetAllCompanyValues() {
		if cv == v {
			return true
		}
	}
	return false
}

// Orbstar represents an orbstar (recognition) in the system
type Orbstar struct {
	ID          uint         `json:"id" gorm:"primaryKey" example:"456"`
	CreatedAt   time.Time    `json:"created_at" gorm:"autoCreateTime" example:"2023-12-01T10:30:00Z"`
	Value       CompanyValue `json:"value" gorm:"type:varchar(20);not null" validate:"required" example:"Innovation"`
	Description string       `json:"description" gorm:"type:text;not null" validate:"required" example:"Great work on the new feature implementation!"`
	GiverID     uint         `json:"-" gorm:"not null"`
	ReceiverID  uint         `json:"-" gorm:"not null"`

	// Relationships
	Giver    User `json:"giver" gorm:"foreignKey:GiverID"`
	Receiver User `json:"receiver" gorm:"foreignKey:ReceiverID"`
}

// OrbstarResponse represents the API response for an orbstar
type OrbstarResponse struct {
	ID          uint        `json:"id" example:"456"`
	CreatedAt   time.Time   `json:"created_at" example:"2023-12-01T10:30:00Z"`
	Value       string      `json:"value" example:"Innovation"`
	Description string      `json:"description" example:"Great work on the new feature implementation!"`
	Giver       UserSummary `json:"giver"`
	Receiver    UserSummary `json:"receiver"`
}

// CreateOrbstarRequest represents the request to create new orbstar(s)
// @Description Request payload for creating new orbstar(s)
// @Description The receiver field can be either a single user ID (integer) or an array of user IDs (max 5)
// @Description Available values: "Self Development", "Innovation", "Initiative", "Authenticity", "Impact", "Reliability"
type CreateOrbstarRequest struct {
	// Receiver can be a single user ID or array of user IDs (max 5 recipients)
	// @Description Single receiver ID or array of receiver IDs (1-5 recipients)
	// @Example 123
	// @Example [123, 456, 789]
	Receiver interface{} `json:"receiver" validate:"required" swaggertype:"primitive,integer" example:"123"`
	// Recognition message describing why the orbstar is being given
	// @Description Recognition message
	// @Example "Excellent leadership during the project crisis"
	Description string `json:"description" validate:"required" example:"Excellent leadership during the project crisis"`
	// Company value being recognized
	// @Description Company value (one of: "Self Development", "Innovation", "Initiative", "Authenticity", "Impact", "Reliability")
	// @Enum Self Development
	// @Enum Innovation
	// @Enum Initiative
	// @Enum Authenticity
	// @Enum Impact
	// @Enum Reliability
	// @Example "Initiative"
	Value CompanyValue `json:"value" validate:"required" example:"Initiative"`
}

// ValueStats represents statistics for company values
type ValueStats map[CompanyValue]int

// UserOrbstarsResponse represents the response for user's orbstars with stats
type UserOrbstarsResponse struct {
	Orbstars   PaginatedOrbstars `json:"orbstars"`
	ValueStats ValueStats        `json:"value_stats"`
}

// PaginatedOrbstars represents paginated orbstar results
type PaginatedOrbstars struct {
	PaginatedResponse
	Items []OrbstarResponse `json:"items"`
}

// TableName returns the table name for the Orbstar model
func (*Orbstar) TableName() string {
	return "orbstar"
}

// ToResponse converts an Orbstar to an OrbstarResponse
func (o *Orbstar) ToResponse() OrbstarResponse {
	return OrbstarResponse{
		ID:          o.ID,
		CreatedAt:   o.CreatedAt,
		Value:       string(o.Value),
		Description: o.Description,
		Giver:       o.Giver.ToSummary(),
		Receiver:    o.Receiver.ToSummary(),
	}
}

// BeforeCreate is a GORM hook that runs before creating an orbstar
func (o *Orbstar) BeforeCreate(tx *gorm.DB) error {
	// Validate that the value is valid
	if !o.Value.IsValid() {
		return gorm.ErrInvalidValue
	}
	return nil
}

// GetReceiverIDs extracts receiver IDs from the request
func (req *CreateOrbstarRequest) GetReceiverIDs() ([]uint, error) {
	var receiverIDs []uint

	switch v := req.Receiver.(type) {
	case float64:
		// Single receiver ID (JSON numbers are float64)
		receiverIDs = append(receiverIDs, uint(v))
	case []interface{}:
		// Multiple receiver IDs
		if len(v) > 5 {
			return nil, gorm.ErrInvalidValue // Max 5 recipients
		}
		for _, id := range v {
			if floatID, ok := id.(float64); ok {
				receiverIDs = append(receiverIDs, uint(floatID))
			} else {
				return nil, gorm.ErrInvalidValue
			}
		}
	default:
		return nil, gorm.ErrInvalidValue
	}

	if len(receiverIDs) == 0 {
		return nil, gorm.ErrInvalidValue
	}

	return receiverIDs, nil
}

// ValuesResponse represents the response for company values endpoint
type ValuesResponse struct {
	Values []string `json:"values" example:"[\"Self Development\", \"Innovation\", \"Initiative\", \"Authenticity\", \"Impact\", \"Reliability\"]"`
}

// GetValuesResponse returns all company values as strings
func GetValuesResponse() ValuesResponse {
	values := GetAllCompanyValues()
	stringValues := make([]string, len(values))
	for i, v := range values {
		stringValues[i] = string(v)
	}
	return ValuesResponse{Values: stringValues}
}
