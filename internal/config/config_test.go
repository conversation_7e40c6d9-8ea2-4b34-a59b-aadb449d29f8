package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLoad_WithEnvFile(t *testing.T) {
	// Create a temporary directory for the test
	tempDir := t.TempDir()

	// Change to the temp directory
	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
	}()

	err = os.Chdir(tempDir)
	require.NoError(t, err)

	// Create a test .env file
	envContent := `# Test environment file
DB_HOST=test-host
DB_PORT=5433
DB_NAME=test_db
DB_USER=test_user
DB_PASSWORD=test_password
DB_SSLMODE=require

PORT=9090
ENVIRONMENT=test

SECRET_KEY=test-secret-key-12345
BACKEND_URL=http://test-backend:9090
FRONTEND_URL=http://test-frontend:3000

OKTA_DOMAIN=test.okta.com
OKTA_CLIENT_ID=test-client-id
OKTA_CLIENT_SECRET=test-client-secret
OKTA_REDIRECT_URI=http://test-backend:9090/login/callback
IDP_ADMIN_GROUP=TestAdmins

SCIM_BEARER_TOKEN=test-scim-token-12345

SLACK_TOKEN=xoxb-test-slack-token
SLACK_NOTIFICATION_CHANNEL_ID=C1234567890
`

	err = os.WriteFile(".env", []byte(envContent), 0644)
	require.NoError(t, err)

	// Load configuration
	cfg, err := Load()
	require.NoError(t, err)
	require.NotNil(t, cfg)

	// Verify that values from .env file are loaded correctly
	assert.Equal(t, 9090, cfg.Port)
	assert.Equal(t, "test", cfg.Environment)

	// Database config
	assert.Equal(t, "test-host", cfg.Database.Host)
	assert.Equal(t, 5433, cfg.Database.Port)
	assert.Equal(t, "test_db", cfg.Database.Name)
	assert.Equal(t, "test_user", cfg.Database.User)
	assert.Equal(t, "test_password", cfg.Database.Password)
	assert.Equal(t, "require", cfg.Database.SSLMode)

	// JWT config
	assert.Equal(t, "test-secret-key-12345", cfg.JWT.SecretKey)
	assert.Equal(t, "http://test-backend:9090", cfg.JWT.BackendURL)
	assert.Equal(t, "http://test-frontend:3000", cfg.JWT.FrontendURL)

	// Okta config
	assert.Equal(t, "test.okta.com", cfg.Okta.Domain)
	assert.Equal(t, "test-client-id", cfg.Okta.ClientID)
	assert.Equal(t, "test-client-secret", cfg.Okta.ClientSecret)
	assert.Equal(t, "http://test-backend:9090/login/callback", cfg.Okta.RedirectURI)
	assert.Equal(t, "TestAdmins", cfg.Okta.AdminGroup)

	// SCIM config
	assert.Equal(t, "test-scim-token-12345", cfg.SCIM.BearerToken)

	// Slack config
	assert.Equal(t, "xoxb-test-slack-token", cfg.Slack.Token)
	assert.Equal(t, "C1234567890", cfg.Slack.ChannelID)
}

func TestLoad_EnvironmentVariablesOverrideEnvFile(t *testing.T) {
	// Create a temporary directory for the test
	tempDir := t.TempDir()

	// Change to the temp directory
	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
	}()

	err = os.Chdir(tempDir)
	require.NoError(t, err)

	// Create a test .env file
	envContent := `DB_PASSWORD=env-file-password
SECRET_KEY=env-file-secret
BACKEND_URL=http://env-file-backend:8080
FRONTEND_URL=http://env-file-frontend:3000
OKTA_DOMAIN=env-file.okta.com
OKTA_CLIENT_ID=env-file-client-id
OKTA_CLIENT_SECRET=env-file-client-secret
OKTA_REDIRECT_URI=http://env-file-backend:8080/login/callback
SCIM_BEARER_TOKEN=env-file-scim-token
`

	err = os.WriteFile(".env", []byte(envContent), 0644)
	require.NoError(t, err)

	// Set environment variables that should override .env file
	os.Setenv("DB_PASSWORD", "env-var-password")
	os.Setenv("SECRET_KEY", "env-var-secret")
	defer func() {
		os.Unsetenv("DB_PASSWORD")
		os.Unsetenv("SECRET_KEY")
	}()

	// Load configuration
	cfg, err := Load()
	require.NoError(t, err)
	require.NotNil(t, cfg)

	// Verify that environment variables override .env file values
	assert.Equal(t, "env-var-password", cfg.Database.Password)
	assert.Equal(t, "env-var-secret", cfg.JWT.SecretKey)

	// Verify that .env file values are used when no environment variable is set
	assert.Equal(t, "http://env-file-backend:8080", cfg.JWT.BackendURL)
	assert.Equal(t, "env-file.okta.com", cfg.Okta.Domain)
}

func TestLoad_WithoutEnvFile(t *testing.T) {
	// Create a temporary directory for the test
	tempDir := t.TempDir()

	// Change to the temp directory
	originalDir, err := os.Getwd()
	require.NoError(t, err)
	defer func() {
		err := os.Chdir(originalDir)
		require.NoError(t, err)
	}()

	err = os.Chdir(tempDir)
	require.NoError(t, err)

	// Set required environment variables
	os.Setenv("DB_PASSWORD", "test-password")
	os.Setenv("SECRET_KEY", "test-secret")
	os.Setenv("BACKEND_URL", "http://localhost:8080")
	os.Setenv("FRONTEND_URL", "http://localhost:3000")
	os.Setenv("OKTA_DOMAIN", "test.okta.com")
	os.Setenv("OKTA_CLIENT_ID", "test-client-id")
	os.Setenv("OKTA_CLIENT_SECRET", "test-client-secret")
	os.Setenv("OKTA_REDIRECT_URI", "http://localhost:8080/login/callback")
	os.Setenv("SCIM_BEARER_TOKEN", "test-scim-token")

	defer func() {
		os.Unsetenv("DB_PASSWORD")
		os.Unsetenv("SECRET_KEY")
		os.Unsetenv("BACKEND_URL")
		os.Unsetenv("FRONTEND_URL")
		os.Unsetenv("OKTA_DOMAIN")
		os.Unsetenv("OKTA_CLIENT_ID")
		os.Unsetenv("OKTA_CLIENT_SECRET")
		os.Unsetenv("OKTA_REDIRECT_URI")
		os.Unsetenv("SCIM_BEARER_TOKEN")
	}()

	// Load configuration (should work without .env file)
	cfg, err := Load()
	require.NoError(t, err)
	require.NotNil(t, cfg)

	// Verify that environment variables are loaded
	assert.Equal(t, "test-password", cfg.Database.Password)
	assert.Equal(t, "test-secret", cfg.JWT.SecretKey)

	// Verify defaults are used
	assert.Equal(t, 8080, cfg.Port)
	assert.Equal(t, "development", cfg.Environment)
	assert.Equal(t, "localhost", cfg.Database.Host)
}
