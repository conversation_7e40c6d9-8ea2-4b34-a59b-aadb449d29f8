package database

import (
	"fmt"
	"log"
	"time"

	"stellar-go/internal/config"
	"stellar-go/internal/models"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Initialize creates and configures the database connection
func Initialize(cfg *config.Config) (*gorm.DB, error) {
	// Configure GORM logger based on global DEBUG flag
	var gormLogger logger.Interface
	if cfg.Debug {
		// Verbose SQL logs for debugging
		gormLogger = logger.Default.LogMode(logger.Info)
	} else {
		// Non-verbose by default (only errors)
		gormLogger = logger.Default.LogMode(logger.Error)
	}

	// Open database connection
	db, err := gorm.Open(postgres.Open(cfg.GetDatabaseDSN()), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// Test the connection
	if err := sqlDB.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	log.Println("Database connection established successfully")

	// Auto-migrate the schema (for development)
	if cfg.Environment != "production" {
		if err := autoMigrate(db); err != nil {
			return nil, fmt.Errorf("failed to auto-migrate: %w", err)
		}
	}

	return db, nil
}

// autoMigrate runs GORM auto-migration for development
func autoMigrate(db *gorm.DB) error {
	log.Println("Running auto-migration...")

	// Create custom types first
	if err := createCustomTypes(db); err != nil {
		return fmt.Errorf("failed to create custom types: %w", err)
	}

	// Auto-migrate models
	err := db.AutoMigrate(
		&models.User{},
		&models.Orbstar{},
	)
	if err != nil {
		return fmt.Errorf("failed to auto-migrate models: %w", err)
	}

	log.Println("Auto-migration completed successfully")
	return nil
}

// createCustomTypes creates custom PostgreSQL types
func createCustomTypes(db *gorm.DB) error {
	// Create Values enum type
	valuesEnum := `
		DO $$ BEGIN
			CREATE TYPE "Values" AS ENUM (
				'Self Development',
				'Innovation',
				'Initiative',
				'Authenticity',
				'Impact',
				'Reliability'
			);
		EXCEPTION
			WHEN duplicate_object THEN null;
		END $$;
	`

	if err := db.Exec(valuesEnum).Error; err != nil {
		return fmt.Errorf("failed to create Values enum: %w", err)
	}

	return nil
}

// Close closes the database connection
func Close(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}
	return sqlDB.Close()
}
