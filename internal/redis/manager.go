package redis

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"stellar-go/internal/config"

	goredis "github.com/redis/go-redis/v9"
)

var client *goredis.Client
var prefix = "stellar"

// InitFromConfig initializes the Redis client from configuration. If REDIS_URL is empty, Redis is disabled.
func InitFromConfig(cfg *config.Config) error {
	// Set cache key prefix (default to "stellar")
	if cfg != nil && cfg.CachePrefix != "" {
		prefix = cfg.CachePrefix
	} else {
		prefix = "stellar"
	}

	if cfg == nil || cfg.RedisURL == "" {
		log.Println("Redis not configured (REDIS_URL empty); distributed PKCE storage disabled")
		client = nil
		return nil
	}

	// Parse REDIS_URL (supports redis:// and rediss://)
	opts, err := goredis.ParseURL(cfg.RedisURL)
	if err != nil {
		return err
	}

	client = goredis.NewClient(opts)
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	if err := client.Ping(ctx).Err(); err != nil {
		// Do not fail hard; log and continue without Redis
		log.Printf("Redis ping failed: %v. Continuing without Redis.", err)
		client = nil
		return nil
	}

	log.Printf("Redis initialized successfully (prefix=%s)", prefix)
	return nil
}

func withPrefix(key string) string {
	if prefix == "" {
		return "stellar:" + key
	}
	return prefix + ":" + key
}

// IsEnabled returns true if Redis client is initialized
func IsEnabled() bool {
	return client != nil
}

// SetJSON stores a value as JSON with the given TTL
func SetJSON(key string, value interface{}, ttl time.Duration) error {
	if client == nil {
		return nil
	}
	b, err := json.Marshal(value)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	return client.Set(ctx, withPrefix(key), b, ttl).Err()
}

// GetJSON retrieves a JSON value into target. Returns (false, nil) if key is missing.
func GetJSON(key string, target interface{}) (bool, error) {
	if client == nil {
		return false, nil
	}
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	cmd := client.Get(ctx, withPrefix(key))
	if err := cmd.Err(); err != nil {
		if err == goredis.Nil {
			return false, nil
		}
		return false, err
	}
	b, err := cmd.Bytes()
	if err != nil {
		return false, err
	}
	if err := json.Unmarshal(b, target); err != nil {
		return false, err
	}
	return true, nil
}

// TTL returns the remaining TTL for the key. If key is missing, returns -2 and nil error.
func TTL(key string) (time.Duration, error) {
	if client == nil {
		return 0, nil
	}
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	return client.TTL(ctx, withPrefix(key)).Result()
}

// Del deletes a key
func Del(key string) error {
	if client == nil {
		return nil
	}
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	return client.Del(ctx, withPrefix(key)).Err()
}
