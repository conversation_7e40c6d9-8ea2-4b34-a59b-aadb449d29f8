package sentry

import (
	"fmt"
	"log"
	"time"

	"stellar-go/internal/config"
	"stellar-go/internal/models"

	"github.com/getsentry/sentry-go"
	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/gin-gonic/gin"
)

// Initialize initializes Sentry with the provided configuration
func Initialize(cfg *config.Config) error {
	// Skip initialization if no DSN is provided
	if cfg.Sentry.DSN == "" {
		log.Println("Sentry DSN not provided, skipping Sentry initialization")
		return nil
	}

	// Determine environment for Sentry
	sentryEnv := cfg.Sentry.Environment
	if sentryEnv == "" {
		sentryEnv = cfg.Environment
	}

	// Initialize Sentry
	err := sentry.Init(sentry.ClientOptions{
		Dsn:         cfg.Sentry.DSN,
		Environment: sentryEnv,
		Release:     cfg.Sentry.Release,
		SampleRate:  cfg.Sentry.SampleRate,
		// Enable distributed tracing and set the sample rate
		EnableTracing:    true,
		TracesSampleRate: cfg.Sentry.TracesSampleRate,
		TracesSampler: sentry.TracesSampler(func(ctx sentry.SamplingContext) float64 {
			name := ""
			if ctx.Span != nil {
				name = ctx.Span.Name
			}
			if name == "GET /" || name == "HEAD /" {
				return 0.0
			}
			return cfg.Sentry.TracesSampleRate
		}),
		Debug:            cfg.Sentry.Debug,
		AttachStacktrace: true,
		BeforeSend: func(event *sentry.Event, hint *sentry.EventHint) *sentry.Event {
			// Filter out test errors in development
			if cfg.Environment == "development" && event.Message == "This is a test error for monitoring" {
				return nil
			}
			return event
		},
	})

	if err != nil {
		return fmt.Errorf("failed to initialize Sentry: %w", err)
	}

	log.Printf("Sentry initialized successfully for environment: %s", sentryEnv)
	return nil
}

// Middleware returns a Gin middleware that integrates with Sentry
func Middleware() gin.HandlerFunc {
	return sentrygin.New(sentrygin.Options{
		Repanic:         true,
		WaitForDelivery: false,
		Timeout:         2 * time.Second,
	})
}

// CaptureError captures an error and sends it to Sentry
func CaptureError(err error) {
	if err != nil {
		sentry.CaptureException(err)
	}
}

// CaptureMessage captures a message and sends it to Sentry
func CaptureMessage(message string, level sentry.Level) {
	sentry.CaptureMessage(message)
}

// SetUser sets user context for Sentry
func SetUser(userID uint, email string) {
	sentry.ConfigureScope(func(scope *sentry.Scope) {
		scope.SetUser(sentry.User{
			ID:    fmt.Sprintf("%d", userID),
			Email: email,
		})
	})
}

// SetUserFromModel sets user context for Sentry from a User model
func SetUserFromModel(user *models.User) {
	if user != nil {
		sentry.ConfigureScope(func(scope *sentry.Scope) {
			scope.SetUser(sentry.User{
				ID:       fmt.Sprintf("%d", user.ID),
				Email:    user.Email,
				Username: user.Email,
				Name:     user.GetFullName(),
			})
		})
	}
}

// UserContextMiddleware sets user context in Sentry for authenticated requests
func UserContextMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Process the request first
		c.Next()

		// After processing, check if full user object is available
		if userObj, exists := c.Get("user"); exists {
			if user, ok := userObj.(*models.User); ok {
				// Set full user context with email and name
				sentry.ConfigureScope(func(scope *sentry.Scope) {
					scope.SetUser(sentry.User{
						ID:       fmt.Sprintf("%d", user.ID),
						Email:    user.Email,
						Username: user.Email,
						Name:     user.GetFullName(),
					})
					scope.SetTag("user_authenticated", "true")
					scope.SetTag("user_admin", fmt.Sprintf("%t", user.Admin))
				})
				return
			}
		}

		// Fallback: check if only user_id is available
		if userID, exists := c.Get("user_id"); exists {
			if id, ok := userID.(uint); ok {
				// Set basic user context with ID only
				sentry.ConfigureScope(func(scope *sentry.Scope) {
					scope.SetUser(sentry.User{
						ID: fmt.Sprintf("%d", id),
					})
					scope.SetTag("user_authenticated", "true")
				})
			}
		}
	}
}

// SetTag sets a tag for Sentry
func SetTag(key, value string) {
	sentry.ConfigureScope(func(scope *sentry.Scope) {
		scope.SetTag(key, value)
	})
}

// SetContext sets additional context for Sentry
func SetContext(key string, context map[string]interface{}) {
	sentry.ConfigureScope(func(scope *sentry.Scope) {
		scope.SetContext(key, context)
	})
}

// Flush waits for all events to be sent to Sentry
func Flush(timeout time.Duration) bool {
	return sentry.Flush(timeout)
}
