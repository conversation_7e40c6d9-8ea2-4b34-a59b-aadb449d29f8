package sentry

import (
	"testing"

	"stellar-go/internal/config"
	"stellar-go/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestInitialize_WithoutDSN(t *testing.T) {
	cfg := &config.Config{
		Sentry: config.SentryConfig{
			DSN: "", // No DSN provided
		},
	}

	err := Initialize(cfg)
	assert.NoError(t, err, "Should not error when DSN is empty")
}

func TestInitialize_WithDSN(t *testing.T) {
	cfg := &config.Config{
		Environment: "test",
		Sentry: config.SentryConfig{
			DSN:              "https://<EMAIL>/123456",
			Environment:      "test",
			SampleRate:       1.0,
			TracesSampleRate: 0.1,
			Debug:            false,
		},
	}

	err := Initialize(cfg)
	assert.NoError(t, err, "Should initialize successfully with valid DSN")
}

func TestSetUserFromModel(t *testing.T) {
	// Test with nil user
	SetUserFromModel(nil)
	// Should not panic

	// Test with valid user
	user := &models.User{
		ID:        123,
		Email:     "<EMAIL>",
		FirstName: "Test",
		LastName:  "User",
	}

	SetUserFromModel(user)
	// Should not panic and should set user context
}

func TestCaptureError(t *testing.T) {
	// Test with nil error
	CaptureError(nil)
	// Should not panic

	// Test with actual error
	err := assert.AnError
	CaptureError(err)
	// Should not panic
}

func TestSetUser(t *testing.T) {
	// Test basic SetUser function
	SetUser(123, "<EMAIL>")
	// Should not panic
}

func TestUserContextMiddleware(t *testing.T) {
	// Test that middleware doesn't panic
	middleware := UserContextMiddleware()
	assert.NotNil(t, middleware)
	// The middleware function should be callable
}
