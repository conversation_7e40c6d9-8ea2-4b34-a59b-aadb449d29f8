package handlers

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"stellar-go/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestHealthHandler_HealthCheckWithNilDB(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test with nil database
	handler := NewHealthHandler(nil)

	// Create test request
	req, _ := http.NewRequest("GET", "/", nil)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Execute
	handler.HealthCheck(c)

	// Assert
	assert.Equal(t, http.StatusServiceUnavailable, w.Code)

	var response models.HealthResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, models.HealthStatusUnhealthy, response.Status)
	assert.Equal(t, "Stellar | Be lekker", response.Message)
	assert.Contains(t, response.Services, "database")
	assert.Equal(t, models.HealthStatusUnhealthy, response.Services["database"].Status)
	assert.NotEmpty(t, response.Services["database"].Error)
}

func TestHealthHandler_checkDatabaseHealthWithNilDB(t *testing.T) {
	// Test with nil database
	handler := NewHealthHandler(nil)
	ctx := context.Background()

	// Execute
	result := handler.checkDatabaseHealth(ctx)

	// Assert
	assert.Equal(t, models.HealthStatusUnhealthy, result.Status)
	assert.NotEmpty(t, result.Error)
	assert.Contains(t, result.Error, "database connection not initialized")
}
