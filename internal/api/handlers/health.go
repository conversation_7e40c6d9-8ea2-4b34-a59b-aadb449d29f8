package handlers

import (
	"context"
	"errors"
	"net/http"
	"time"

	"stellar-go/internal/models"
	"stellar-go/internal/sentry"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	db *gorm.DB
}

// NewHealthHandler creates a new health handler
func NewHealthHandler(db *gorm.DB) *HealthHandler {
	return &HealthHandler{
		db: db,
	}
}

// HealthCheck handles GET /healthz
// @Summary Health check endpoint
// @Description Comprehensive health check that tests database connectivity
// @Tags Health
// @Accept json
// @Produce json
// @Success 200 {object} models.HealthResponse
// @Failure 503 {object} models.HealthResponse
// @Router /healthz [get]
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	services := make(map[string]models.ComponentHealth)
	overallStatus := models.HealthStatusHealthy

	// Check PostgreSQL connectivity
	dbHealth := h.checkDatabaseHealth(ctx)
	services["database"] = dbHealth
	if dbHealth.Status == models.HealthStatusUnhealthy {
		overallStatus = models.HealthStatusUnhealthy
	}

	response := models.HealthResponse{
		Status:   overallStatus,
		Message:  "Stellar | Be lekker",
		Services: services,
	}

	statusCode := http.StatusOK
	if overallStatus == models.HealthStatusUnhealthy {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, response)
}

// FailEndpoint handles GET /fail
// @Summary Test error endpoint
// @Description Endpoint for testing error monitoring (Sentry)
// @Tags Health
// @Accept json
// @Produce json
// @Success 500 {object} models.ErrorResponse
// @Router /fail [get]
func (h *HealthHandler) FailEndpoint(c *gin.Context) {
	// Create a test error and report it to Sentry
	testErr := errors.New("this is a test error for monitoring")
	sentry.CaptureError(testErr)

	c.JSON(http.StatusInternalServerError, models.ErrorResponse{
		Error: "This is a test error for monitoring",
		Code:  "test_error",
	})
}

// checkDatabaseHealth performs a health check on the PostgreSQL database
func (h *HealthHandler) checkDatabaseHealth(ctx context.Context) models.ComponentHealth {
	if h.db == nil {
		return models.ComponentHealth{
			Status: models.HealthStatusUnhealthy,
			Error:  "database connection not initialized",
		}
	}

	// Get the underlying sql.DB to perform a ping
	sqlDB, err := h.db.DB()
	if err != nil {
		return models.ComponentHealth{
			Status: models.HealthStatusUnhealthy,
			Error:  "failed to get underlying database connection: " + err.Error(),
		}
	}

	// Perform a ping with context timeout
	if err := sqlDB.PingContext(ctx); err != nil {
		return models.ComponentHealth{
			Status: models.HealthStatusUnhealthy,
			Error:  "database ping failed: " + err.Error(),
		}
	}

	// Optionally, perform a simple query to ensure the database is responsive
	var result int
	if err := h.db.WithContext(ctx).Raw("SELECT 1").Scan(&result).Error; err != nil {
		return models.ComponentHealth{
			Status: models.HealthStatusUnhealthy,
			Error:  "database query failed: " + err.Error(),
		}
	}

	return models.ComponentHealth{
		Status: models.HealthStatusHealthy,
	}
}
