package handlers

import (
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestHomepageHandler_Homepage(t *testing.T) {
	// Arrange
	gin.SetMode(gin.TestMode)

	// Change to project root directory for the test
	originalDir, _ := os.Getwd()
	projectRoot := filepath.Join(originalDir, "../../../")
	os.Chdir(projectRoot)
	defer os.Chdir(originalDir)

	router := gin.New()
	handler := NewHomepageHandler()
	router.GET("/", handler.Homepage)

	// Act
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/", nil)
	router.ServeHTTP(w, req)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Header().Get("Content-Type"), "text/html")
	assert.Contains(t, w.Body.String(), "STELLAR")
	assert.Contains(t, w.Body.String(), "Be lekker, share an Orbstar")
	assert.Contains(t, w.Body.String(), "<!DOCTYPE html>")
	assert.Contains(t, w.Body.String(), "/static/assets/logo.svg")
	assert.Contains(t, w.Body.String(), "stellar-title")
}
