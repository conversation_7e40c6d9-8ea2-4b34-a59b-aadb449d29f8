package handlers

import (
	"errors"
	"net/http"
	"strconv"

	"stellar-go/internal/middleware"
	"stellar-go/internal/models"
	"stellar-go/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UserHandler handles user-related endpoints
type UserHandler struct {
	userService *services.UserService
}

// NewUserHandler creates a new user handler
func NewUserHandler(userService *services.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// GetCurrentUser handles GET /user
// @Summary Get current user profile
// @Description Retrieve the authenticated user's profile information
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} models.UserProfile
// @Failure 401 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /user [get]
func (h *UserHandler) GetCurrentUser(c *gin.Context) {
	userID, exists := middleware.GetUserID(c)
	if !exists {
		c.J<PERSON>N(http.StatusUnauthorized, models.ErrorResponse{
			Error: "Authentication required",
			Code:  "auth_required",
		})
		return
	}

	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error: "User not found",
				Code:  "user_not_found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error: "Failed to retrieve user",
				Code:  "internal_error",
			})
		}
		return
	}

	profile := models.UserProfile{User: *user}
	c.JSON(http.StatusOK, profile)
}

// GetUsers handles GET /users
// @Summary List all users
// @Description Retrieve a list of all active users with optional filtering
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param first_name query string false "Filter by first name (case-insensitive partial match)"
// @Param last_name query string false "Filter by last name (case-insensitive partial match)"
// @Param email query string false "Filter by email (case-insensitive partial match)"
// @Success 200 {array} models.User
// @Failure 401 {object} models.ErrorResponse
// @Router /users [get]
func (h *UserHandler) GetUsers(c *gin.Context) {
	var filter models.UserListFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid query parameters",
			Code:  "invalid_params",
		})
		return
	}

	users, err := h.userService.GetUsers(&filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to retrieve users",
			Code:  "internal_error",
		})
		return
	}

	c.JSON(http.StatusOK, users)
}

// GetUserCount handles GET /users/count
// @Summary Get user count
// @Description Retrieve the total number of users in the system
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {integer} int64
// @Failure 401 {object} models.ErrorResponse
// @Router /users/count [get]
func (h *UserHandler) GetUserCount(c *gin.Context) {
	count, err := h.userService.GetUserCount()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to get user count",
			Code:  "internal_error",
		})
		return
	}

	c.JSON(http.StatusOK, count)
}

// GetUserByID handles GET /users/:id (for admin use)
// @Summary Get user by ID
// @Description Retrieve a specific user by ID (admin only)
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "User ID"
// @Success 200 {object} models.User
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /users/{id} [get]
func (h *UserHandler) GetUserByID(c *gin.Context) {
	// Check if user is admin (this would be done by admin middleware in practice)
	currentUserID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error: "Authentication required",
			Code:  "auth_required",
		})
		return
	}

	// Get current user to check admin status
	currentUser, err := h.userService.GetUserByID(currentUserID)
	if err != nil || !currentUser.Admin {
		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error: "Admin privileges required",
			Code:  "admin_required",
		})
		return
	}

	// Parse user ID from path
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid user ID",
			Code:  "invalid_user_id",
		})
		return
	}

	user, err := h.userService.GetUserByID(uint(userID))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error: "User not found",
				Code:  "user_not_found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error: "Failed to retrieve user",
				Code:  "internal_error",
			})
		}
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUser handles PUT /users/:id (for admin use)
// @Summary Update user
// @Description Update a user's information (admin only)
// @Tags Users
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "User ID"
// @Param user body models.UpdateUserRequest true "User update data"
// @Success 200 {object} models.User
// @Failure 400 {object} models.ErrorResponse
// @Failure 401 {object} models.ErrorResponse
// @Failure 403 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	// Check if user is admin
	currentUserID, exists := middleware.GetUserID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, models.ErrorResponse{
			Error: "Authentication required",
			Code:  "auth_required",
		})
		return
	}

	currentUser, err := h.userService.GetUserByID(currentUserID)
	if err != nil || !currentUser.Admin {
		c.JSON(http.StatusForbidden, models.ErrorResponse{
			Error: "Admin privileges required",
			Code:  "admin_required",
		})
		return
	}

	// Parse user ID from path
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid user ID",
			Code:  "invalid_user_id",
		})
		return
	}

	// Parse request body
	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Error: "Invalid request body",
			Code:  "invalid_request",
		})
		return
	}

	// Get existing user
	user, err := h.userService.GetUserByID(uint(userID))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, models.ErrorResponse{
				Error: "User not found",
				Code:  "user_not_found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.ErrorResponse{
				Error: "Failed to retrieve user",
				Code:  "internal_error",
			})
		}
		return
	}

	// Update fields if provided
	if req.FirstName != nil {
		user.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		user.LastName = *req.LastName
	}
	if req.Active != nil {
		user.Active = *req.Active
	}
	if req.Admin != nil {
		user.Admin = *req.Admin
	}

	// Save updated user
	updatedUser, err := h.userService.UpdateUser(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Error: "Failed to update user",
			Code:  "internal_error",
		})
		return
	}

	c.JSON(http.StatusOK, updatedUser)
}
