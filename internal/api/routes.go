package api

import (
	"stellar-go/internal/api/handlers"
	"stellar-go/internal/auth"
	"stellar-go/internal/config"
	"stellar-go/internal/middleware"
	"stellar-go/internal/scim"
	"stellar-go/internal/services"
	"stellar-go/pkg/slack"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	"github.com/swaggo/gin-swagger"
	"gorm.io/gorm"

	_ "stellar-go/docs" // Import generated docs
)

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config) {
	// Initialize services
	userService := services.NewUserService(db)
	tokenService := services.NewTokenService(db)
	slackClient := slack.NewClient(cfg)
	orbstarService := services.NewOrbstarService(db, userService, slackClient)

	// Initialize auth managers
	jwtManager := auth.NewJWTManager(cfg)
	oktaManager, err := auth.NewOktaManager(cfg)
	if err != nil {
		// In test environment, we can continue without Ok<PERSON>
		if cfg.Environment == "test" {
			oktaManager = nil
		} else {
			panic("Failed to initialize Okta manager: " + err.Error())
		}
	}

	// Initialize handlers
	healthHandler := handlers.NewHealthHandler(db)
	homepageHandler := handlers.NewHomepageHandler()
	authHandler := handlers.NewAuthHandler(jwtManager, oktaManager, userService, tokenService, cfg)
	userHandler := handlers.NewUserHandler(userService)
	orbstarHandler := handlers.NewOrbstarHandler(orbstarService, userService)
	scimHandler := scim.NewSCIMHandler(userService, cfg)
	slackHandler := handlers.NewSlackHandler(cfg, userService, orbstarService)

	// Add global middleware
	router.Use(middleware.CORSMiddleware(cfg))
	router.Use(middleware.RequestIDMiddleware())

	// Swagger documentation
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Static files
	router.Static("/static", "./static")

	// Public routes (no authentication required)
	public := router.Group("/")
	{
		// Homepage and health check endpoints
		public.GET("/", homepageHandler.Homepage)
		public.GET("/healthz", healthHandler.HealthCheck)
		public.GET("/fail", healthHandler.FailEndpoint)

		// Authentication endpoints
		public.GET("/login", authHandler.Login)
		public.GET("/idp-login", authHandler.HandleIDPInitiatedLogin)
		public.GET("/login/callback", authHandler.LoginCallback)
		public.POST("/auth/refresh", authHandler.RefreshToken)
		public.POST("/auth/exchange", authHandler.ExchangeSession)

		// Slack slash commands & interactivity (verified by Slack signing secret)
		slackGroup := public.Group("/slack")
		slackGroup.Use(auth.SlackVerificationMiddleware(cfg.Slack.SigningSecret))
		{
			slackGroup.POST("/commands/orbstar", slackHandler.HandleOrbstarSlash)
			slackGroup.POST("/interactivity", slackHandler.HandleInteractivity)
		}

		public.POST("/auth/logout", authHandler.Logout)
	}

	// SCIM endpoints (separate authentication)
	scimGroup := router.Group("/scim/v2")
	scimGroup.Use(scimHandler.AuthMiddleware())
	{
		scimGroup.GET("/Users", scimHandler.GetUsers)
		scimGroup.GET("/Users/<USER>", scimHandler.GetUser)
		scimGroup.POST("/Users", scimHandler.CreateUser)
		scimGroup.PUT("/Users/<USER>", scimHandler.UpdateUser)
		scimGroup.PATCH("/Users/<USER>", scimHandler.PatchUser)
		scimGroup.DELETE("/Users/<USER>", scimHandler.DeleteUser)
	}

	// Protected routes (JWT authentication required)
	protected := router.Group("/")
	protected.Use(middleware.AuthMiddlewareWithUserService(jwtManager, userService))
	{
		// User endpoints
		protected.GET("/user", userHandler.GetCurrentUser)
		protected.GET("/users", userHandler.GetUsers)
		protected.GET("/users/count", userHandler.GetUserCount)
		protected.GET("/users/:id", userHandler.GetUserByID) // Admin only
		protected.PUT("/users/:id", userHandler.UpdateUser)  // Admin only

		// Company values endpoint
		protected.GET("/values", orbstarHandler.GetValues)

		// Orbstar endpoints
		protected.GET("/orbstars", orbstarHandler.GetOrbstars)
		protected.POST("/orbstars", orbstarHandler.CreateOrbstars)
		protected.DELETE("/orbstars", orbstarHandler.DeleteOrbstar) // Admin only
		protected.GET("/orbstars/received", orbstarHandler.GetReceivedOrbstars)
		protected.GET("/orbstars/given", orbstarHandler.GetGivenOrbstars)
		protected.GET("/orbstars/leaderboard", orbstarHandler.GetLeaderboard)
	}
}
