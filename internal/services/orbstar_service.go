package services

import (
	"fmt"

	"stellar-go/internal/models"
	"stellar-go/pkg/slack"

	"gorm.io/gorm"
)

// OrbstarService handles orbstar-related business logic
type OrbstarService struct {
	db          *gorm.DB
	userService *UserService
	slackClient *slack.Client
}

// NewOrbstarService creates a new orbstar service
func NewOrbstarService(db *gorm.DB, userService *UserService, slackClient *slack.Client) *OrbstarService {
	return &OrbstarService{
		db:          db,
		userService: userService,
		slackClient: slackClient,
	}
}

// GetOrbstars retrieves orbstars with pagination
func (s *OrbstarService) GetOrbstars(page int) (*models.PaginatedOrbstars, error) {
	const pageSize = 15
	offset := (page - 1) * pageSize

	var orbstars []models.Orbstar
	var totalCount int64

	// Get total count
	if err := s.db.Model(&models.Orbstar{}).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count orbstars: %w", err)
	}

	// Get paginated orbstars with preloaded relationships
	err := s.db.Preload("Giver").Preload("Receiver").
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&orbstars).Error
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve orbstars: %w", err)
	}

	// Convert to response format
	items := make([]models.OrbstarResponse, len(orbstars))
	for i, orbstar := range orbstars {
		items[i] = orbstar.ToResponse()
	}

	pagination := models.CalculatePagination(page, pageSize, int(totalCount))

	return &models.PaginatedOrbstars{
		PaginatedResponse: pagination,
		Items:             items,
	}, nil
}

// GetUserReceivedOrbstars retrieves orbstars received by a user
func (s *OrbstarService) GetUserReceivedOrbstars(userID uint, page int) (*models.UserOrbstarsResponse, error) {
	const pageSize = 15
	offset := (page - 1) * pageSize

	var orbstars []models.Orbstar
	var totalCount int64

	// Get total count for received orbstars
	if err := s.db.Model(&models.Orbstar{}).Where("receiver_id = ?", userID).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count received orbstars: %w", err)
	}

	// Get paginated received orbstars
	err := s.db.Preload("Giver").Preload("Receiver").
		Where("receiver_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&orbstars).Error
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve received orbstars: %w", err)
	}

	// Convert to response format
	items := make([]models.OrbstarResponse, len(orbstars))
	for i, orbstar := range orbstars {
		items[i] = orbstar.ToResponse()
	}

	pagination := models.CalculatePagination(page, pageSize, int(totalCount))

	// Get value statistics for received orbstars
	valueStats, err := s.getValueStats(userID, "receiver_id")
	if err != nil {
		return nil, fmt.Errorf("failed to get value stats: %w", err)
	}

	return &models.UserOrbstarsResponse{
		Orbstars: models.PaginatedOrbstars{
			PaginatedResponse: pagination,
			Items:             items,
		},
		ValueStats: valueStats,
	}, nil
}

// GetUserGivenOrbstars retrieves orbstars given by a user
func (s *OrbstarService) GetUserGivenOrbstars(userID uint, page int) (*models.UserOrbstarsResponse, error) {
	const pageSize = 15
	offset := (page - 1) * pageSize

	var orbstars []models.Orbstar
	var totalCount int64

	// Get total count for given orbstars
	if err := s.db.Model(&models.Orbstar{}).Where("giver_id = ?", userID).Count(&totalCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count given orbstars: %w", err)
	}

	// Get paginated given orbstars
	err := s.db.Preload("Giver").Preload("Receiver").
		Where("giver_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).Limit(pageSize).
		Find(&orbstars).Error
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve given orbstars: %w", err)
	}

	// Convert to response format
	items := make([]models.OrbstarResponse, len(orbstars))
	for i, orbstar := range orbstars {
		items[i] = orbstar.ToResponse()
	}

	pagination := models.CalculatePagination(page, pageSize, int(totalCount))

	// Get value statistics for given orbstars
	valueStats, err := s.getValueStats(userID, "giver_id")
	if err != nil {
		return nil, fmt.Errorf("failed to get value stats: %w", err)
	}

	return &models.UserOrbstarsResponse{
		Orbstars: models.PaginatedOrbstars{
			PaginatedResponse: pagination,
			Items:             items,
		},
		ValueStats: valueStats,
	}, nil
}

// GetLeaderboard retrieves the company values leaderboard
func (s *OrbstarService) GetLeaderboard() (models.ValueStats, error) {
	var results []struct {
		Value string
		Count int
	}

	err := s.db.Model(&models.Orbstar{}).
		Select("value, COUNT(*) as count").
		Group("value").
		Find(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get leaderboard: %w", err)
	}

	// Convert to ValueStats format
	valueStats := make(models.ValueStats)
	for _, result := range results {
		valueStats[models.CompanyValue(result.Value)] = result.Count
	}

	// Ensure all values are present (with 0 count if not found)
	for _, value := range models.GetAllCompanyValues() {
		if _, exists := valueStats[value]; !exists {
			valueStats[value] = 0
		}
	}

	return valueStats, nil
}

// CreateOrbstars creates one or more orbstars
func (s *OrbstarService) CreateOrbstars(giverID uint, req *models.CreateOrbstarRequest) ([]models.OrbstarResponse, error) {
	// Get receiver IDs
	receiverIDs, err := req.GetReceiverIDs()
	if err != nil {
		return nil, fmt.Errorf("invalid receiver IDs: %w", err)
	}

	// Validate that giver is not in the receiver list (prevent self-recognition)
	for _, receiverID := range receiverIDs {
		if receiverID == giverID {
			return nil, fmt.Errorf("receiver may not be the same as the user making the request")
		}
	}

	// Validate that all receivers exist
	if err := s.userService.ValidateUsersExist(receiverIDs); err != nil {
		return nil, err
	}

	// Validate company value
	if !req.Value.IsValid() {
		return nil, fmt.Errorf("invalid company value: %s", req.Value)
	}

	// Create orbstars in a transaction
	var createdOrbstars []models.Orbstar
	err = s.db.Transaction(func(tx *gorm.DB) error {
		for _, receiverID := range receiverIDs {
			orbstar := models.Orbstar{
				GiverID:     giverID,
				ReceiverID:  receiverID,
				Value:       req.Value,
				Description: req.Description,
			}

			if err := tx.Create(&orbstar).Error; err != nil {
				return fmt.Errorf("failed to create orbstar: %w", err)
			}

			// Preload relationships for response
			if err := tx.Preload("Giver").Preload("Receiver").First(&orbstar, orbstar.ID).Error; err != nil {
				return fmt.Errorf("failed to load orbstar relationships: %w", err)
			}

			createdOrbstars = append(createdOrbstars, orbstar)
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	// Send notifications asynchronously
	go s.sendNotifications(createdOrbstars)

	// Convert to response format
	responses := make([]models.OrbstarResponse, len(createdOrbstars))
	for i, orbstar := range createdOrbstars {
		responses[i] = orbstar.ToResponse()
	}

	return responses, nil
}

// DeleteOrbstar deletes an orbstar (admin only)
func (s *OrbstarService) DeleteOrbstar(orbstarID uint) error {
	result := s.db.Delete(&models.Orbstar{}, orbstarID)
	if result.Error != nil {
		return fmt.Errorf("failed to delete orbstar: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// getValueStats calculates value statistics for a user
func (s *OrbstarService) getValueStats(userID uint, column string) (models.ValueStats, error) {
	var results []struct {
		Value string
		Count int
	}

	err := s.db.Model(&models.Orbstar{}).
		Select("value, COUNT(*) as count").
		Where(column+" = ?", userID).
		Group("value").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	// Convert to ValueStats format
	valueStats := make(models.ValueStats)
	for _, result := range results {
		valueStats[models.CompanyValue(result.Value)] = result.Count
	}

	// Ensure all values are present (with 0 count if not found)
	for _, value := range models.GetAllCompanyValues() {
		if _, exists := valueStats[value]; !exists {
			valueStats[value] = 0
		}
	}

	return valueStats, nil
}

// sendNotifications sends Slack notifications for new orbstars
func (s *OrbstarService) sendNotifications(orbstars []models.Orbstar) {
	if s.slackClient == nil {
		return // Slack not configured
	}

	// Send individual direct messages to each recipient
	for _, orbstar := range orbstars {
		err := s.slackClient.SendOrbstarDirectMessage(&orbstar)
		if err != nil {
			// Log error but don't fail the request
			fmt.Printf("Failed to send Slack direct message: %v\n", err)
		}
	}

	// Send a single channel notification for all orbstars (if multiple recipients)
	if len(orbstars) > 0 {
		err := s.slackClient.SendOrbstarChannelNotification(orbstars)
		if err != nil {
			// Log error but don't fail the request
			fmt.Printf("Failed to send Slack channel notification: %v\n", err)
		}
	}
}
