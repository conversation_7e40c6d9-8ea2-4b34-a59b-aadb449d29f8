package auth

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// SlackVerificationMiddleware verifies incoming Slack requests using the signing secret.
// It validates the Slack-Signature and Slack-Request-Timestamp headers to prevent tampering
// and replay attacks. Rejects requests older than 5 minutes or with invalid signatures.
func SlackVerificationMiddleware(signingSecret string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if signingSecret == "" {
			debugf("SlackVerification: signing secret is not configured")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Slack signing not configured", "code": "slack_not_configured"})
			return
		}

		// Extract headers
		timestamp := c.Request.Header.Get("X-Slack-Request-Timestamp")
		sigHeader := c.Request.Header.Get("X-Slack-Signature")

		if timestamp == "" || sigHeader == "" {
			debugf("SlackVerification: missing headers timestamp=%q signature_present=%t", timestamp, sigHeader != "")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Missing Slack signature headers", "code": "missing_signature"})
			return
		}

		// Verify timestamp is within 5 minutes to prevent replay attacks
		ts, err := strconv.ParseInt(timestamp, 10, 64)
		if err != nil {
			debugf("SlackVerification: invalid timestamp %q: %v", timestamp, err)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid timestamp", "code": "invalid_timestamp"})
			return
		}
		now := time.Now().Unix()
		skew := abs(now - ts)
		debugf("SlackVerification: timestamp check now=%d ts=%d skew=%ds", now, ts, skew)
		if skew > 60*5 {
			debugf("SlackVerification: expired timestamp (skew=%ds)", skew)
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Expired timestamp", "code": "expired_timestamp"})
			return
		}

		// Read the raw body (Slack signs the raw payload). Gin may have already read it into c.Request.Body.
		// We'll read and then replace the body so downstream handlers can read it again.
		var bodyBytes []byte
		if c.Request.Body != nil {
			bodyBytes, err = io.ReadAll(c.Request.Body)
			if err != nil {
				debugf("SlackVerification: failed reading body: %v", err)
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unable to read body", "code": "read_body_failed"})
				return
			}
		}
		c.Request.Body = io.NopCloser(strings.NewReader(string(bodyBytes)))

		// Build the basestring: v0:timestamp:body
		base := strings.Join([]string{"v0", timestamp, string(bodyBytes)}, ":")

		mac := hmac.New(sha256.New, []byte(signingSecret))
		mac.Write([]byte(base))
		computed := "v0=" + hex.EncodeToString(mac.Sum(nil))

		// Add detailed debugging before signature comparison
		debugf("SlackVerification: signing_secret=%q (len=%d)", signingSecret, len(signingSecret))
		debugf("SlackVerification: body_length=%d", len(bodyBytes))
		debugf("SlackVerification: body_content=%q", string(bodyBytes))
		debugf("SlackVerification: base_string=%q", base)
		debugf("SlackVerification: timestamp=%q", timestamp)

		// For debugging, log a hash of the base string and compare signatures
		baseHash := sha256.Sum256([]byte(base))
		debugf("SlackVerification: base_sha256=%x provided_sig=%q computed_sig=%q", baseHash[:8], sigHeader, computed)

		// Compare signatures in constant-time
		if !hmac.Equal([]byte(computed), []byte(sigHeader)) {
			debugf("SlackVerification: signature mismatch")
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid Slack signature", "code": "invalid_signature"})
			return
		}

		// Restore body for form parsing in handlers
		c.Request.Body = io.NopCloser(strings.NewReader(string(bodyBytes)))

		debugf("SlackVerification: verification passed")
		c.Next()
	}
}

func abs(n int64) int64 {
	if n < 0 {
		return -n
	}
	return n
}

// debugf logs only when Gin is in DebugMode (usually DEBUG=1)
func debugf(format string, args ...any) {
	if gin.Mode() == gin.DebugMode {
		log.Printf(format, args...)
	}
}
