# Sentry Error Reporting Setup

This document explains how to configure and use Sentry for error reporting in the Stellar Go application.

## Overview

Sentry has been integrated into the application to provide real-time error tracking and performance monitoring. The integration includes:

- Automatic error capture for panics and unhandled errors
- User context tracking for authenticated requests
- Custom error reporting capabilities
- Environment-specific configuration

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file or configuration:

```bash
# Required - Your Sentry DSN from your Sentry project
SENTRY_DSN=https://<EMAIL>/project-id

# Optional - Environment name (defaults to ENVIRONMENT value)
SENTRY_ENVIRONMENT=production

# Optional - Release version for tracking deployments
SENTRY_RELEASE=stellar-go@1.0.0

# Optional - Error sampling rate (0.0 to 1.0, default: 1.0)
SENTRY_SAMPLE_RATE=1.0

# Optional - Performance monitoring sampling rate (0.0 to 1.0, default: 0.1)
SENTRY_TRACES_SAMPLE_RATE=0.1

# Optional - Enable debug mode (default: false)
SENTRY_DEBUG=false
```

### YAML Configuration

Alternatively, add to your `config.yaml`:

```yaml
# Sentry configuration (optional - for error reporting)
sentry_dsn: "https://<EMAIL>/project-id"
sentry_environment: "production"
sentry_release: "stellar-go@1.0.0"
sentry_sample_rate: 1.0
sentry_traces_sample_rate: 0.1
sentry_debug: false
```

## Getting Your Sentry DSN

1. Create a Sentry account at [sentry.io](https://sentry.io)
2. Create a new project for your Go application
3. Copy the DSN from your project settings
4. Add it to your configuration

## Features

### Automatic Error Capture

The application automatically captures:
- Panics and unhandled errors
- HTTP errors and exceptions
- Database connection errors
- Authentication failures

### User Context

For authenticated requests, Sentry will automatically include:
- **User ID** - The authenticated user's database ID
- **User Email** - The user's email address from the database
- **User Name** - The user's full name (first + last name)
- **Username** - The user's email (used as username)
- **Authentication Status** - Tagged as authenticated
- **Admin Status** - Whether the user has admin privileges

### Custom Error Reporting

You can manually report errors in your code:

```go
import "stellar-go/internal/sentry"

// Report an error
err := someOperation()
if err != nil {
    sentry.CaptureError(err)
    // Handle error normally
}

// Report a message
sentry.CaptureMessage("Something important happened", sentry.LevelInfo)

// Set additional context
sentry.SetTag("operation", "user_creation")
sentry.SetContext("request_data", map[string]interface{}{
    "user_id": 123,
    "action": "create_user",
})
```

### Test Endpoint

The application includes a test endpoint to verify Sentry integration:

```bash
curl http://localhost:8080/fail
```

This will generate a test error that should appear in your Sentry dashboard.

## Environment-Specific Behavior

### Development
- All errors are captured (sample_rate: 1.0)
- Test errors from `/fail` endpoint are filtered out
- Debug logging available

### Production
- Configurable error sampling
- Performance monitoring enabled
- User context included for better debugging

## Monitoring and Alerts

Once configured, you can:
- View errors in real-time on your Sentry dashboard
- Set up alerts for critical errors
- Track error trends and performance
- Get detailed stack traces and context

## Troubleshooting

### Sentry Not Receiving Events

1. Verify your DSN is correct
2. Check that `SENTRY_DSN` environment variable is set
3. Ensure your application has internet access
4. Check application logs for Sentry initialization messages

### Missing User Context

User context is only available for authenticated requests. Ensure:
- The request includes a valid JWT token
- The user is active in the system
- The authentication middleware is working correctly

**Note**: The enhanced user context (including email and name) is automatically retrieved from the database during authentication, so no additional configuration is needed.

### Performance Impact

Sentry is configured with reasonable defaults:
- 2-second timeout for event delivery
- Non-blocking error reporting
- Configurable sampling rates

For high-traffic applications, consider reducing the sample rates.
