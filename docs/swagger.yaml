basePath: /
definitions:
  models.CompanyValue:
    enum:
    - Self Development
    - Innovation
    - Initiative
    - Authenticity
    - Impact
    - Reliability
    type: string
    x-enum-varnames:
    - SelfDevelopment
    - Innovation
    - Initiative
    - Authenticity
    - Impact
    - Reliability
  models.ComponentHealth:
    properties:
      error:
        example: connection timeout
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.HealthStatus'
        example: healthy
    type: object
  models.CreateOrbstarRequest:
    description: 'Request payload for creating new orbstar(s) The receiver field can
      be either a single user ID (integer) or an array of user IDs (max 5) Available
      values: "Self Development", "Innovation", "Initiative", "Authenticity", "Impact",
      "Reliability"'
    properties:
      description:
        description: |-
          Recognition message describing why the orbstar is being given
          @Description Recognition message
          @Example "Excellent leadership during the project crisis"
        example: Excellent leadership during the project crisis
        type: string
      receiver:
        description: |-
          Receiver can be a single user ID or array of user IDs (max 5 recipients)
          @Description Single receiver ID or array of receiver IDs (1-5 recipients)
          @Example 123
          @Example [123, 456, 789]
        example: 123
        type: integer
      value:
        allOf:
        - $ref: '#/definitions/models.CompanyValue'
        description: |-
          Company value being recognized
          @Description Company value (one of: "Self Development", "Innovation", "Initiative", "Authenticity", "Impact", "Reliability")
          @Enum Self Development
          @Enum Innovation
          @Enum Initiative
          @Enum Authenticity
          @Enum Impact
          @Enum Reliability
          @Example "Initiative"
        example: Initiative
    required:
    - description
    - receiver
    - value
    type: object
  models.ErrorResponse:
    properties:
      code:
        example: invalid_token
        type: string
      error:
        example: Invalid token payload
        type: string
    type: object
  models.HealthResponse:
    properties:
      message:
        example: Stellar | Be lekker
        type: string
      services:
        additionalProperties:
          $ref: '#/definitions/models.ComponentHealth'
        type: object
      status:
        allOf:
        - $ref: '#/definitions/models.HealthStatus'
        example: healthy
    type: object
  models.HealthStatus:
    enum:
    - healthy
    - unhealthy
    type: string
    x-enum-varnames:
    - HealthStatusHealthy
    - HealthStatusUnhealthy
  models.LogoutRequest:
    properties:
      refresh:
        example: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...
        type: string
    required:
    - refresh
    type: object
  models.OrbstarResponse:
    properties:
      created_at:
        example: "2023-12-01T10:30:00Z"
        type: string
      description:
        example: Great work on the new feature implementation!
        type: string
      giver:
        $ref: '#/definitions/models.UserSummary'
      id:
        example: 456
        type: integer
      receiver:
        $ref: '#/definitions/models.UserSummary'
      value:
        example: Innovation
        type: string
    type: object
  models.PaginatedOrbstars:
    properties:
      count:
        example: 42
        type: integer
      has_next:
        example: true
        type: boolean
      has_prev:
        example: false
        type: boolean
      items:
        items:
          $ref: '#/definitions/models.OrbstarResponse'
        type: array
      page:
        example: 1
        type: integer
    type: object
  models.RefreshTokenRequest:
    properties:
      access:
        example: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...
        type: string
      refresh:
        example: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...
        type: string
    required:
    - access
    - refresh
    type: object
  models.SessionExchangeRequest:
    properties:
      session_id:
        example: abc123def456ghi789
        type: string
    required:
    - session_id
    type: object
  models.SessionExchangeResponse:
    properties:
      access:
        example: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...
        type: string
      refresh:
        example: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  models.SuccessResponse:
    properties:
      status:
        example: Ok
        type: string
    type: object
  models.TokenResponse:
    properties:
      access:
        example: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...
        type: string
      refresh:
        example: eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  models.UpdateUserRequest:
    properties:
      active:
        example: true
        type: boolean
      admin:
        example: false
        type: boolean
      first_name:
        example: John
        maxLength: 24
        type: string
      last_name:
        example: Doe
        maxLength: 24
        type: string
    type: object
  models.User:
    properties:
      active:
        example: true
        type: boolean
      admin:
        example: false
        type: boolean
      email:
        example: <EMAIL>
        maxLength: 64
        type: string
      first_name:
        example: John
        maxLength: 24
        type: string
      id:
        example: 123
        type: integer
      last_name:
        example: Doe
        maxLength: 24
        type: string
    required:
    - email
    - first_name
    - last_name
    type: object
  models.UserOrbstarsResponse:
    properties:
      orbstars:
        $ref: '#/definitions/models.PaginatedOrbstars'
      value_stats:
        $ref: '#/definitions/models.ValueStats'
    type: object
  models.UserProfile:
    properties:
      active:
        example: true
        type: boolean
      admin:
        example: false
        type: boolean
      email:
        example: <EMAIL>
        maxLength: 64
        type: string
      first_name:
        example: John
        maxLength: 24
        type: string
      id:
        example: 123
        type: integer
      last_name:
        example: Doe
        maxLength: 24
        type: string
    required:
    - email
    - first_name
    - last_name
    type: object
  models.UserSummary:
    properties:
      email:
        example: <EMAIL>
        type: string
      first_name:
        example: John
        type: string
      last_name:
        example: Doe
        type: string
    type: object
  models.ValueStats:
    additionalProperties:
      type: integer
    type: object
  models.ValuesResponse:
    properties:
      values:
        example:
        - '["Self Development"'
        - ' "Innovation"'
        - ' "Initiative"'
        - ' "Authenticity"'
        - ' "Impact"'
        - ' "Reliability"]'
        items:
          type: string
        type: array
    type: object
  scim.SCIMEmail:
    properties:
      display:
        type: string
      primary:
        type: boolean
      type:
        type: string
      value:
        type: string
    type: object
  scim.SCIMError:
    properties:
      detail:
        type: string
      schemas:
        items:
          type: string
        type: array
      status:
        type: string
    type: object
  scim.SCIMListResponse:
    properties:
      Resources:
        items:
          $ref: '#/definitions/scim.SCIMUser'
        type: array
      itemsPerPage:
        type: integer
      schemas:
        items:
          type: string
        type: array
      startIndex:
        type: integer
      totalResults:
        type: integer
    type: object
  scim.SCIMMeta:
    properties:
      created:
        type: string
      lastModified:
        type: string
      location:
        type: string
      resourceType:
        type: string
    type: object
  scim.SCIMName:
    properties:
      familyName:
        type: string
      givenName:
        type: string
    type: object
  scim.SCIMPatchOperation:
    properties:
      op:
        type: string
      path:
        type: string
      value:
        additionalProperties: true
        type: object
    type: object
  scim.SCIMPatchRequest:
    properties:
      Operations:
        items:
          $ref: '#/definitions/scim.SCIMPatchOperation'
        type: array
      schemas:
        items:
          type: string
        type: array
    type: object
  scim.SCIMUser:
    properties:
      active:
        type: boolean
      displayName:
        type: string
      emails:
        items:
          $ref: '#/definitions/scim.SCIMEmail'
        type: array
      externalId:
        type: string
      groups:
        items:
          type: string
        type: array
      id:
        type: string
      locale:
        type: string
      meta:
        $ref: '#/definitions/scim.SCIMMeta'
      name:
        $ref: '#/definitions/scim.SCIMName'
      schemas:
        items:
          type: string
        type: array
      userName:
        type: string
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: Byte Orbit
    url: https://byteorbit.com
  description: Stellar is a peer-to-peer recognition system that allows employees
    to give and receive "Orbstars" - digital recognition tokens based on company values.
  termsOfService: http://swagger.io/terms/
  title: Stellar Backend API
  version: "1.0"
paths:
  /auth/exchange:
    post:
      consumes:
      - application/json
      description: Exchanges a temporary session ID for JWT access and refresh tokens
      parameters:
      - description: Session exchange request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.SessionExchangeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SessionExchangeResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Exchange session for JWT tokens
      tags:
      - Authentication
  /auth/logout:
    post:
      consumes:
      - application/json
      description: Invalidates the user's refresh token and logs them out
      parameters:
      - description: Logout request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.LogoutRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Logout user
      tags:
      - Authentication
  /auth/refresh:
    post:
      consumes:
      - application/json
      description: Exchange refresh token for new access and refresh tokens
      parameters:
      - description: Refresh token request
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.RefreshTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.TokenResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Refresh JWT tokens
      tags:
      - Authentication
  /fail:
    get:
      consumes:
      - application/json
      description: Endpoint for testing error monitoring (Sentry)
      produces:
      - application/json
      responses:
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Test error endpoint
      tags:
      - Health
  /healthz:
    get:
      consumes:
      - application/json
      description: Comprehensive health check that tests database connectivity
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.HealthResponse'
        "503":
          description: Service Unavailable
          schema:
            $ref: '#/definitions/models.HealthResponse'
      summary: Health check endpoint
      tags:
      - Health
  /idp-login:
    get:
      consumes:
      - application/json
      description: Handles login initiated from Okta's dashboard while preserving
        return URL
      parameters:
      - description: URL to redirect to after successful authentication
        in: query
        name: return_to
        type: string
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to standard login flow
          schema:
            type: string
      summary: Handle IDP-initiated login
      tags:
      - Authentication
  /login:
    get:
      consumes:
      - application/json
      description: Redirects to Okta for authentication
      parameters:
      - description: URL to redirect to after successful authentication
        in: query
        name: return_to
        type: string
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to Okta login
          schema:
            type: string
      summary: Initiate Okta login
      tags:
      - Authentication
  /login/callback:
    get:
      consumes:
      - application/json
      description: Processes the authorization code from Okta and returns JWT tokens
      parameters:
      - description: Authorization code from Okta
        in: query
        name: code
        required: true
        type: string
      - description: State parameter for CSRF protection
        in: query
        name: state
        type: string
      produces:
      - application/json
      responses:
        "302":
          description: Redirect to frontend with tokens
          schema:
            type: string
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      summary: Handle Okta callback
      tags:
      - Authentication
  /orbstars:
    delete:
      consumes:
      - application/json
      description: Delete a specific orbstar. Only administrators can perform this
        action.
      parameters:
      - description: ID of the orbstar to delete
        in: query
        name: orbstar_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Delete an orbstar (Admin only)
      tags:
      - Orbstars
    get:
      consumes:
      - application/json
      description: Retrieve a paginated list of all orbstars in the system
      parameters:
      - default: 1
        description: Page number for pagination
        in: query
        minimum: 1
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.PaginatedOrbstars'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: List all orbstars
      tags:
      - Orbstars
    post:
      consumes:
      - application/json
      description: |-
        Give recognition by creating one or more orbstars. You can recognize up to 5 people in a single request.
        Each recipient will receive a separate orbstar with the same value and description.

        **Request Examples:**

        Single recipient:
        ```json
        {
        "receiver": 123,
        "description": "Outstanding work on the quarterly presentation",
        "value": "Impact"
        }
        ```

        Multiple recipients:
        ```json
        {
        "receiver": [123, 456, 789],
        "description": "Excellent teamwork during the product launch",
        "value": "Initiative"
        }
        ```

        **Available Values:** "Self Development", "Innovation", "Initiative", "Authenticity", "Impact", "Reliability"
      parameters:
      - description: Orbstar creation data
        in: body
        name: orbstar
        required: true
        schema:
          $ref: '#/definitions/models.CreateOrbstarRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Successfully created orbstar(s)
          schema:
            items:
              $ref: '#/definitions/models.OrbstarResponse'
            type: array
        "400":
          description: Bad request - validation errors
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized - authentication required
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Create new orbstar(s)
      tags:
      - Orbstars
  /orbstars/given:
    get:
      consumes:
      - application/json
      description: Retrieve all orbstars given by the authenticated user, along with
        value statistics
      parameters:
      - default: 1
        description: Page number for pagination
        in: query
        minimum: 1
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserOrbstarsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get orbstars given by current user
      tags:
      - Orbstars
  /orbstars/leaderboard:
    get:
      consumes:
      - application/json
      description: Retrieve the total count of orbstars given for each company value
        across the entire organization
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ValueStats'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get company values leaderboard
      tags:
      - Orbstars
  /orbstars/received:
    get:
      consumes:
      - application/json
      description: Retrieve all orbstars received by the authenticated user, along
        with value statistics
      parameters:
      - default: 1
        description: Page number for pagination
        in: query
        minimum: 1
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserOrbstarsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get orbstars received by current user
      tags:
      - Orbstars
  /scim/v2/Users:
    get:
      consumes:
      - application/json
      description: Retrieve users using SCIM 2.0 protocol with optional filtering
      parameters:
      - default: 1
        description: Start index for pagination
        in: query
        name: startIndex
        type: integer
      - default: 100
        description: Number of results per page
        in: query
        name: count
        type: integer
      - description: SCIM filter expression (supports userName eq)
        in: query
        name: filter
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/scim.SCIMListResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/scim.SCIMError'
      security:
      - BearerAuth: []
      summary: List users via SCIM
      tags:
      - SCIM
    post:
      consumes:
      - application/json
      description: Create a new user using SCIM 2.0 protocol
      parameters:
      - description: User data
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/scim.SCIMUser'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/scim.SCIMUser'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/scim.SCIMError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/scim.SCIMError'
        "409":
          description: Conflict
          schema:
            $ref: '#/definitions/scim.SCIMError'
      security:
      - BearerAuth: []
      summary: Create user via SCIM
      tags:
      - SCIM
  /scim/v2/Users/<USER>
    delete:
      consumes:
      - application/json
      description: Delete a user using SCIM 2.0 protocol
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "204":
          description: No Content
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/scim.SCIMError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/scim.SCIMError'
      security:
      - BearerAuth: []
      summary: Delete user via SCIM
      tags:
      - SCIM
    get:
      consumes:
      - application/json
      description: Retrieve a specific user using SCIM 2.0 protocol
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/scim.SCIMUser'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/scim.SCIMError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/scim.SCIMError'
      security:
      - BearerAuth: []
      summary: Get user by ID via SCIM
      tags:
      - SCIM
    patch:
      consumes:
      - application/json
      description: Update specific user attributes using SCIM 2.0 PATCH protocol
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: PATCH operations
        in: body
        name: patch
        required: true
        schema:
          $ref: '#/definitions/scim.SCIMPatchRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/scim.SCIMUser'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/scim.SCIMError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/scim.SCIMError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/scim.SCIMError'
      security:
      - BearerAuth: []
      summary: Patch user via SCIM
      tags:
      - SCIM
    put:
      consumes:
      - application/json
      description: Update an existing user using SCIM 2.0 protocol
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: string
      - description: User data
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/scim.SCIMUser'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/scim.SCIMUser'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/scim.SCIMError'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/scim.SCIMError'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/scim.SCIMError'
      security:
      - BearerAuth: []
      summary: Update user via SCIM
      tags:
      - SCIM
  /user:
    get:
      consumes:
      - application/json
      description: Retrieve the authenticated user's profile information
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.UserProfile'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get current user profile
      tags:
      - Users
  /users:
    get:
      consumes:
      - application/json
      description: Retrieve a list of all active users with optional filtering
      parameters:
      - description: Filter by first name (case-insensitive partial match)
        in: query
        name: first_name
        type: string
      - description: Filter by last name (case-insensitive partial match)
        in: query
        name: last_name
        type: string
      - description: Filter by email (case-insensitive partial match)
        in: query
        name: email
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.User'
            type: array
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: List all users
      tags:
      - Users
  /users/{id}:
    get:
      consumes:
      - application/json
      description: Retrieve a specific user by ID (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user by ID
      tags:
      - Users
    put:
      consumes:
      - application/json
      description: Update a user's information (admin only)
      parameters:
      - description: User ID
        in: path
        name: id
        required: true
        type: integer
      - description: User update data
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/models.UpdateUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.User'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/models.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Update user
      tags:
      - Users
  /users/count:
    get:
      consumes:
      - application/json
      description: Retrieve the total number of users in the system
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: integer
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get user count
      tags:
      - Users
  /values:
    get:
      consumes:
      - application/json
      description: Retrieve the list of company values that can be used for recognition
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ValuesResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/models.ErrorResponse'
      security:
      - BearerAuth: []
      summary: Get company values
      tags:
      - Values
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
