{"swagger": "2.0", "info": {"description": "Stellar is a peer-to-peer recognition system that allows employees to give and receive \"Orbstars\" - digital recognition tokens based on company values.", "title": "Stellar Backend API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "Byte Orbit", "url": "https://byteorbit.com", "email": "<EMAIL>"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/", "paths": {"/auth/exchange": {"post": {"description": "Exchanges a temporary session ID for JWT access and refresh tokens", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Exchange session for JWT tokens", "parameters": [{"description": "Session exchange request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.SessionExchangeRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SessionExchangeResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/auth/logout": {"post": {"security": [{"BearerAuth": []}], "description": "Invalidates the user's refresh token and logs them out", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Logout user", "parameters": [{"description": "Logout request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.LogoutRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.SuccessResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/auth/refresh": {"post": {"description": "Exchange refresh token for new access and refresh tokens", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Refresh JWT tokens", "parameters": [{"description": "Refresh token request", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.RefreshTokenRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.TokenResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/fail": {"get": {"description": "Endpoint for testing error monitoring (Sentry)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Health"], "summary": "Test error endpoint", "responses": {"500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/healthz": {"get": {"description": "Comprehensive health check that tests database connectivity", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Health"], "summary": "Health check endpoint", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.HealthResponse"}}, "503": {"description": "Service Unavailable", "schema": {"$ref": "#/definitions/models.HealthResponse"}}}}}, "/idp-login": {"get": {"description": "Handles login initiated from <PERSON><PERSON>'s dashboard while preserving return URL", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Handle IDP-initiated login", "parameters": [{"type": "string", "description": "URL to redirect to after successful authentication", "name": "return_to", "in": "query"}], "responses": {"302": {"description": "Redirect to standard login flow", "schema": {"type": "string"}}}}}, "/login": {"get": {"description": "Redirects to Okta for authentication", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "Initiate <PERSON><PERSON> login", "parameters": [{"type": "string", "description": "URL to redirect to after successful authentication", "name": "return_to", "in": "query"}], "responses": {"302": {"description": "Redirect to Okta login", "schema": {"type": "string"}}}}}, "/login/callback": {"get": {"description": "Processes the authorization code from Ok<PERSON> and returns JWT tokens", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Authentication"], "summary": "<PERSON><PERSON> callback", "parameters": [{"type": "string", "description": "Authorization code from Okta", "name": "code", "in": "query", "required": true}, {"type": "string", "description": "State parameter for CSRF protection", "name": "state", "in": "query"}], "responses": {"302": {"description": "Redirect to frontend with tokens", "schema": {"type": "string"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orbstars": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a paginated list of all orbstars in the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Orbstars"], "summary": "List all orbstars", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number for pagination", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.PaginatedOrbstars"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Give recognition by creating one or more orbstars. You can recognize up to 5 people in a single request.\nEach recipient will receive a separate orbstar with the same value and description.\n\n**Request Examples:**\n\nSingle recipient:\n```json\n{\n\"receiver\": 123,\n\"description\": \"Outstanding work on the quarterly presentation\",\n\"value\": \"Impact\"\n}\n```\n\nMultiple recipients:\n```json\n{\n\"receiver\": [123, 456, 789],\n\"description\": \"Excellent teamwork during the product launch\",\n\"value\": \"Initiative\"\n}\n```\n\n**Available Values:** \"Self Development\", \"Innovation\", \"Initiative\", \"Authenticity\", \"Impact\", \"Reliability\"", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Orbstars"], "summary": "Create new orbstar(s)", "parameters": [{"description": "Orbstar creation data", "name": "orbstar", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.CreateOrbstarRequest"}}], "responses": {"201": {"description": "Successfully created orbstar(s)", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.OrbstarResponse"}}}, "400": {"description": "Bad request - validation errors", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized - authentication required", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a specific orbstar. Only administrators can perform this action.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Orbstars"], "summary": "Delete an orbstar (Admin only)", "parameters": [{"type": "integer", "description": "ID of the orbstar to delete", "name": "orbstar_id", "in": "query", "required": true}], "responses": {"204": {"description": "No Content"}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orbstars/given": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all orbstars given by the authenticated user, along with value statistics", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Orbstars"], "summary": "Get orbstars given by current user", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number for pagination", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.UserOrbstarsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orbstars/leaderboard": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve the total count of orbstars given for each company value across the entire organization", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Orbstars"], "summary": "Get company values leaderboard", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.ValueStats"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/orbstars/received": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve all orbstars received by the authenticated user, along with value statistics", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Orbstars"], "summary": "Get orbstars received by current user", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number for pagination", "name": "page", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.UserOrbstarsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/scim/v2/Users": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve users using SCIM 2.0 protocol with optional filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SCIM"], "summary": "List users via SCIM", "parameters": [{"type": "integer", "default": 1, "description": "Start index for pagination", "name": "startIndex", "in": "query"}, {"type": "integer", "default": 100, "description": "Number of results per page", "name": "count", "in": "query"}, {"type": "string", "description": "SCIM filter expression (supports userName eq)", "name": "filter", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/scim.SCIMListResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/scim.SCIMError"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new user using SCIM 2.0 protocol", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SCIM"], "summary": "Create user via SCIM", "parameters": [{"description": "User data", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/scim.SCIMUser"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/scim.SCIMUser"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/scim.SCIMError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/scim.SCIMError"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/scim.SCIMError"}}}}}, "/scim/v2/Users/<USER>": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a specific user using SCIM 2.0 protocol", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SCIM"], "summary": "Get user by ID via SCIM", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/scim.SCIMUser"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/scim.SCIMError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/scim.SCIMError"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update an existing user using SCIM 2.0 protocol", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SCIM"], "summary": "Update user via SCIM", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User data", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/scim.SCIMUser"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/scim.SCIMUser"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/scim.SCIMError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/scim.SCIMError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/scim.SCIMError"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a user using SCIM 2.0 protocol", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SCIM"], "summary": "Delete user via SCIM", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/scim.SCIMError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/scim.SCIMError"}}}}, "patch": {"security": [{"BearerAuth": []}], "description": "Update specific user attributes using SCIM 2.0 PATCH protocol", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["SCIM"], "summary": "Patch user via SCIM", "parameters": [{"type": "string", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "PATCH operations", "name": "patch", "in": "body", "required": true, "schema": {"$ref": "#/definitions/scim.SCIMPatchRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/scim.SCIMUser"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/scim.SCIMError"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/scim.SCIMError"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/scim.SCIMError"}}}}}, "/user": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve the authenticated user's profile information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get current user profile", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.UserProfile"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/users": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a list of all active users with optional filtering", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "List all users", "parameters": [{"type": "string", "description": "Filter by first name (case-insensitive partial match)", "name": "first_name", "in": "query"}, {"type": "string", "description": "Filter by last name (case-insensitive partial match)", "name": "last_name", "in": "query"}, {"type": "string", "description": "Filter by email (case-insensitive partial match)", "name": "email", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/models.User"}}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/users/count": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve the total number of users in the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user count", "responses": {"200": {"description": "OK", "schema": {"type": "integer"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/users/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a specific user by ID (admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Get user by ID", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.User"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update a user's information (admin only)", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Users"], "summary": "Update user", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User update data", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/models.UpdateUserRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.User"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}, "/values": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve the list of company values that can be used for recognition", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Values"], "summary": "Get company values", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/models.ValuesResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/models.ErrorResponse"}}}}}}, "definitions": {"models.CompanyValue": {"type": "string", "enum": ["Self Development", "Innovation", "Initiative", "Authenticity", "Impact", "Reliability"], "x-enum-varnames": ["SelfDevelopment", "Innovation", "Initiative", "Authenticity", "Impact", "Reliability"]}, "models.ComponentHealth": {"type": "object", "properties": {"error": {"type": "string", "example": "connection timeout"}, "status": {"allOf": [{"$ref": "#/definitions/models.HealthStatus"}], "example": "healthy"}}}, "models.CreateOrbstarRequest": {"description": "Request payload for creating new orbstar(s) The receiver field can be either a single user ID (integer) or an array of user IDs (max 5) Available values: \"Self Development\", \"Innovation\", \"Initiative\", \"Authenticity\", \"Impact\", \"Reliability\"", "type": "object", "required": ["description", "receiver", "value"], "properties": {"description": {"description": "Recognition message describing why the orbstar is being given\n@Description Recognition message\n@Example \"Excellent leadership during the project crisis\"", "type": "string", "example": "Excellent leadership during the project crisis"}, "receiver": {"description": "Receiver can be a single user ID or array of user IDs (max 5 recipients)\n@Description Single receiver ID or array of receiver IDs (1-5 recipients)\n@Example 123\n@Example [123, 456, 789]", "type": "integer", "example": 123}, "value": {"description": "Company value being recognized\n@Description Company value (one of: \"Self Development\", \"Innovation\", \"Initiative\", \"Authenticity\", \"Impact\", \"Reliability\")\n@Enum Self Development\n@Enum Innovation\n@Enum Initiative\n@Enum Authenticity\n@Enum Impact\n@Enum Reliability\n@Example \"Initiative\"", "allOf": [{"$ref": "#/definitions/models.CompanyValue"}], "example": "Initiative"}}}, "models.ErrorResponse": {"type": "object", "properties": {"code": {"type": "string", "example": "invalid_token"}, "error": {"type": "string", "example": "Invalid token payload"}}}, "models.HealthResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "Stellar | Be lekker"}, "services": {"type": "object", "additionalProperties": {"$ref": "#/definitions/models.ComponentHealth"}}, "status": {"allOf": [{"$ref": "#/definitions/models.HealthStatus"}], "example": "healthy"}}}, "models.HealthStatus": {"type": "string", "enum": ["healthy", "unhealthy"], "x-enum-varnames": ["HealthStatusHealthy", "HealthStatusUnhealthy"]}, "models.LogoutRequest": {"type": "object", "required": ["refresh"], "properties": {"refresh": {"type": "string", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."}}}, "models.OrbstarResponse": {"type": "object", "properties": {"created_at": {"type": "string", "example": "2023-12-01T10:30:00Z"}, "description": {"type": "string", "example": "Great work on the new feature implementation!"}, "giver": {"$ref": "#/definitions/models.UserSummary"}, "id": {"type": "integer", "example": 456}, "receiver": {"$ref": "#/definitions/models.UserSummary"}, "value": {"type": "string", "example": "Innovation"}}}, "models.PaginatedOrbstars": {"type": "object", "properties": {"count": {"type": "integer", "example": 42}, "has_next": {"type": "boolean", "example": true}, "has_prev": {"type": "boolean", "example": false}, "items": {"type": "array", "items": {"$ref": "#/definitions/models.OrbstarResponse"}}, "page": {"type": "integer", "example": 1}}}, "models.RefreshTokenRequest": {"type": "object", "required": ["access", "refresh"], "properties": {"access": {"type": "string", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."}, "refresh": {"type": "string", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."}}}, "models.SessionExchangeRequest": {"type": "object", "required": ["session_id"], "properties": {"session_id": {"type": "string", "example": "abc123def456ghi789"}}}, "models.SessionExchangeResponse": {"type": "object", "properties": {"access": {"type": "string", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."}, "refresh": {"type": "string", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."}}}, "models.SuccessResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "Ok"}}}, "models.TokenResponse": {"type": "object", "properties": {"access": {"type": "string", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."}, "refresh": {"type": "string", "example": "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9..."}}}, "models.UpdateUserRequest": {"type": "object", "properties": {"active": {"type": "boolean", "example": true}, "admin": {"type": "boolean", "example": false}, "first_name": {"type": "string", "maxLength": 24, "example": "<PERSON>"}, "last_name": {"type": "string", "maxLength": 24, "example": "<PERSON><PERSON>"}}}, "models.User": {"type": "object", "required": ["email", "first_name", "last_name"], "properties": {"active": {"type": "boolean", "example": true}, "admin": {"type": "boolean", "example": false}, "email": {"type": "string", "maxLength": 64, "example": "<EMAIL>"}, "first_name": {"type": "string", "maxLength": 24, "example": "<PERSON>"}, "id": {"type": "integer", "example": 123}, "last_name": {"type": "string", "maxLength": 24, "example": "<PERSON><PERSON>"}}}, "models.UserOrbstarsResponse": {"type": "object", "properties": {"orbstars": {"$ref": "#/definitions/models.PaginatedOrbstars"}, "value_stats": {"$ref": "#/definitions/models.ValueStats"}}}, "models.UserProfile": {"type": "object", "required": ["email", "first_name", "last_name"], "properties": {"active": {"type": "boolean", "example": true}, "admin": {"type": "boolean", "example": false}, "email": {"type": "string", "maxLength": 64, "example": "<EMAIL>"}, "first_name": {"type": "string", "maxLength": 24, "example": "<PERSON>"}, "id": {"type": "integer", "example": 123}, "last_name": {"type": "string", "maxLength": 24, "example": "<PERSON><PERSON>"}}}, "models.UserSummary": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}}}, "models.ValueStats": {"type": "object", "additionalProperties": {"type": "integer"}}, "models.ValuesResponse": {"type": "object", "properties": {"values": {"type": "array", "items": {"type": "string"}, "example": ["[\"Self Development\"", " \"Innovation\"", " \"Initiative\"", " \"Authenticity\"", " \"Impact\"", " \"Reliability\"]"]}}}, "scim.SCIMEmail": {"type": "object", "properties": {"display": {"type": "string"}, "primary": {"type": "boolean"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "scim.SCIMError": {"type": "object", "properties": {"detail": {"type": "string"}, "schemas": {"type": "array", "items": {"type": "string"}}, "status": {"type": "string"}}}, "scim.SCIMListResponse": {"type": "object", "properties": {"Resources": {"type": "array", "items": {"$ref": "#/definitions/scim.SCIMUser"}}, "itemsPerPage": {"type": "integer"}, "schemas": {"type": "array", "items": {"type": "string"}}, "startIndex": {"type": "integer"}, "totalResults": {"type": "integer"}}}, "scim.SCIMMeta": {"type": "object", "properties": {"created": {"type": "string"}, "lastModified": {"type": "string"}, "location": {"type": "string"}, "resourceType": {"type": "string"}}}, "scim.SCIMName": {"type": "object", "properties": {"familyName": {"type": "string"}, "givenName": {"type": "string"}}}, "scim.SCIMPatchOperation": {"type": "object", "properties": {"op": {"type": "string"}, "path": {"type": "string"}, "value": {"type": "object", "additionalProperties": true}}}, "scim.SCIMPatchRequest": {"type": "object", "properties": {"Operations": {"type": "array", "items": {"$ref": "#/definitions/scim.SCIMPatchOperation"}}, "schemas": {"type": "array", "items": {"type": "string"}}}}, "scim.SCIMUser": {"type": "object", "properties": {"active": {"type": "boolean"}, "displayName": {"type": "string"}, "emails": {"type": "array", "items": {"$ref": "#/definitions/scim.SCIMEmail"}}, "externalId": {"type": "string"}, "groups": {"type": "array", "items": {"type": "string"}}, "id": {"type": "string"}, "locale": {"type": "string"}, "meta": {"$ref": "#/definitions/scim.SCIMMeta"}, "name": {"$ref": "#/definitions/scim.SCIMName"}, "schemas": {"type": "array", "items": {"type": "string"}}, "userName": {"type": "string"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}